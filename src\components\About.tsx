import React from 'react';
import { motion } from 'framer-motion';
import { Factory, Award, History, Leaf } from 'lucide-react';

const About = () => {
  return (
    <section className="py-20 bg-white" id="about">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div 
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5 }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl font-bold text-gray-900 mb-4">About Jayita Group</h2>
          <p className="text-xl text-gray-600">Our heritage and mustard oil tradition</p>
        </motion.div>

        {/* Company History Section */}
        <div className="mb-20">
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5 }}
            className="flex items-center mb-8"
          >
            <History className="text-yellow-600 mr-4" size={32} />
            <h3 className="text-2xl font-semibold">Our Company History</h3>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: 0.2 }}
              className="bg-yellow-50 p-8 rounded-xl"
            >
              <h4 className="text-xl font-bold mb-4">Foundation</h4>
              <p className="text-gray-700">
              Jayita Group has been inspired by our old ancient Indian practice of eating pure and natural foods, using
natural oil, Ghee, Butter and spices found in in various seeds, flowers and tree bark. The company
believes that the Strength comes from the power and efficacy comes from the traditional method to
offer refined products for the modern population. Initially deated by two visionary entrepreneurs, Mr.
Sovon Sarkar and Mr. Debdutta Chowdhury, subsequently Mr Koushik Sardar an eminent Entrepreneur
and Mr Kutubuddin Mondal a renowned Lawyer joined the hand with their Ideas.
              </p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: 0.4 }}
              className="bg-yellow-50 p-8 rounded-xl"
            >
              <h4 className="text-xl font-bold mb-4">Growth & Expansion</h4>
              <p className="text-gray-700">
              Over the next 3–5 years, we anticipate significant growth driven by increasing consumer
awareness of healthy eating, rising demand for plant-based and cold-pressed oils, and expanding
distribution channels both domestically and internationally. With investments in modern
production technology, sustainable sourcing, and brand development, we aim to achieve:<br></br>
 Annual revenue growth of 100% 1st year, then 40 to 50% on 2nd year onward.<br></br>
 Expansion into 3–5 new regional markets<br></br>
 Launch of 2–3 new product lines, including organic and fortified oils<br></br>
 Increase in production capacity by 30%<br></br>
 Strengthening our supply chain and farmer partnerships to support scalability.<br></br>
              </p>
            </motion.div>
          </div>
        </div>

        {/* Mustard Oil Heritage Section */}
        <div>
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5 }}
            className="flex items-center mb-8"
          >
            <Leaf className="text-yellow-600 mr-4" size={32} />
            <h3 className="text-2xl font-semibold">Mustard Oil in West Bengal</h3>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: 0.2 }}
              className="bg-yellow-50 p-8 rounded-xl"
            >
              <h4 className="text-xl font-bold mb-4">Cultural Significance in West Bengal</h4>
              <p className="text-gray-700">
                Mustard oil has been the soul of Bengali cuisine for centuries. Its distinctive pungent aroma and flavor are integral to traditional dishes like shorshe ilish (hilsa fish in mustard sauce) and various pickles.<br></br>
                 Soya oil is widely used in urban and semi-urban areas due to its affordability compared to
mustard oil.<br></br>
 Price-sensitive demand in Bengal reacts to international price movements (as India imports a
lot of soya oil, mainly from Argentina and Brazil).<br></br>
 Producer states (like MP and Rajasthan) impact the supply and price of raw materials (mustard
seeds, soybean).<br></br>
 Consumer states (like West Bengal) impact the retail demand and oil refinery operations.<br></br>
 High demand in Bengal can lead to logistical prioritization, price variation in Eastern India, and
strategic imports by companies.
              </p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: 0.4 }}
              className="bg-yellow-50 p-8 rounded-xl"
            >
              <h4 className="text-xl font-bold mb-4">Historical Significance</h4>
              <p className="text-gray-700">
              India imports ~60% of its edible oil requirement , hence global price fluctuations a key factor. MSP,
import duties, and bans (e.g., on blending oils) affect state-level dynamics. In some states, edible oils
are part of welfare schemes, affecting local demand and prices. West Bengal is not a major producer of
mustard seeds but is a major consumer of mustard oil. The state's cuisine and cultural preferences
(especially Bengali cuisine) drive strong demand. High demand from West Bengal affects market prices
and supply chains, especially during festivals or crop failures. Demand spikes during Durga Puja (Bengal),
Diwali, etc., which impacts local prices.
The impact of West Bengal and other Indian states on mustard and soya oil markets can be looked at
from both production and consumption perspectives. Here's a interesting information.
1. Rajasthan – Largest producer of mustard seeds (over 40% of India's total). Whereas largest
Consumer.
2. Madhya Pradesh – Often called the soy state of India, accounting for around 50%+ of
production.
              </p>
            </motion.div>
          </div>
        </div>

        {/* Milestones */}
        <div className="mt-16 grid grid-cols-1 md:grid-cols-3 gap-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5, delay: 0.2 }}
            className="bg-white p-6 rounded-lg shadow text-center"
          >
            <Factory className="mx-auto text-yellow-600 mb-4" size={40} />
            <h4 className="text-xl font-semibold mb-2">3 Production Units</h4>
            <p className="text-gray-600">Across West Bengal</p>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5, delay: 0.4 }}
            className="bg-white p-6 rounded-lg shadow text-center"
          >
            <Award className="mx-auto text-yellow-600 mb-4" size={40} />
            <h4 className="text-xl font-semibold mb-2">3 Generations</h4>
            <p className="text-gray-600">Of mustard oil expertise</p>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5, delay: 0.6 }}
            className="bg-white p-6 rounded-lg shadow text-center"
          >
            <Leaf className="mx-auto text-yellow-600 mb-4" size={40} />
            <h4 className="text-xl font-semibold mb-2">100% Pure</h4>
            <p className="text-gray-600">Cold-pressed mustard oil</p>
          </motion.div>
        </div>
      </div>
    </section>
  );
};

export default About;
