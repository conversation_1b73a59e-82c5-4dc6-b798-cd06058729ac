import { Variants } from 'framer-motion';

// Core Animation System
export const staggerContainer = (staggerChildren = 0.1, delayChildren = 0): Variants => ({
  hidden: {},
  show: {
    transition: {
      staggerChildren,
      delayChildren,
    },
  },
});

// Premium Animation Presets
export const fadeIn3D = (direction: 'up'|'down'|'left'|'right', delay = 0, duration = 0.5): Variants => ({
  hidden: {
    opacity: 0,
    y: direction === 'up' ? 40 : direction === 'down' ? -40 : 0,
    x: direction === 'left' ? 40 : direction === 'right' ? -40 : 0,
    rotateX: 15,
    rotateY: 15,
    perspective: '1000px',
  },
  show: {
    opacity: 1,
    y: 0,
    x: 0,
    rotateX: 0,
    rotateY: 0,
    transition: {
      type: 'spring',
      damping: 20,
      stiffness: 100,
      delay,
      duration,
    },
  },
});

export const cardFlip = {
  hidden: { rotateY: 90, opacity: 0 },
  show: {
    rotateY: 0,
    opacity: 1,
    transition: {
      type: 'spring',
      stiffness: 100,
      damping: 15,
    }
  }
};

export const fluidWave = (intensity = 0.1) => ({
  hidden: { 
    pathLength: 0,
    pathOffset: 1,
    opacity: 0
  },
  show: {
    pathLength: 1,
    pathOffset: 0,
    opacity: 1,
    transition: {
      pathLength: { 
        type: 'spring',
        duration: 1.5,
        bounce: 0
      },
      opacity: { duration: 0.1 }
    }
  }
});

// Scroll-linked animations
export const scrollReveal = {
  offscreen: {
    y: 50,
    opacity: 0,
  },
  onscreen: {
    y: 0,
    opacity: 1,
    transition: {
      type: 'spring',
      duration: 0.8,
    }
  }
};

// Micro-interactions
export const hoverScale = {
  whileHover: { scale: 1.05 },
  whileTap: { scale: 0.95 }
};

export const hoverTilt = {
  whileHover: {
    rotateZ: 2,
    transition: { duration: 0.2 }
  }
};

export const glowEffect = {
  hidden: { boxShadow: '0 0 0 0 rgba(255,215,0,0)' },
  show: {
    boxShadow: '0 0 20px 5px rgba(255,215,0,0.7)',
    transition: { duration: 0.5, yoyo: Infinity }
  }
};
