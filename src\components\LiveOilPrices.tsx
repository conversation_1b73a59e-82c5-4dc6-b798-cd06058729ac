import React, { useState, useEffect } from 'react';
import { Clock, ExternalLink } from 'lucide-react';
import './LiveOilPrices.css';

interface OilPrice {
  name: string;
  price: string;
}

interface LiveOilPricesProps {
  prices?: OilPrice[];
}

const LiveOilPrices: React.FC<LiveOilPricesProps> = ({ prices: propPrices }) => {
  const defaultPrices: OilPrice[] = [
    { name: 'Sunflower Oil', price: '145.3' },
    { name: 'Mustard Oil', price: '153.3' },
    { name: 'Kachi Ghani Mustard Oil', price: '157.3' },
    { name: 'Soybean Oil', price: '136.8' },
    { name: 'Palm Oil', price: '131.3' },
    { name: 'Cooking Oil', price: '141.3' }
  ];

  const prices = propPrices || defaultPrices;
  const [currentTime, setCurrentTime] = useState<string>('');

  useEffect(() => {
    const updateTime = () => {
      // Get current time in UTC
      const now = new Date();

      // Create a formatter for Indian Standard Time (UTC+5:30)
      const formatter = new Intl.DateTimeFormat('en-IN', {
        timeZone: 'Asia/Kolkata',
        hour12: true,
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        day: '2-digit',
        month: 'short',
        year: 'numeric'
      });

      // Format the time using the IST timezone
      setCurrentTime(formatter.format(now));
    };

    updateTime();
    const timer = setInterval(updateTime, 1000);
    return () => clearInterval(timer);
  }, []);

  const handleViewAllPrices = () => {
    // Open the oil prices selection page
    window.open('./oil-prices-selection.html', '_blank');
  };

  return (
    <section className="py-12 bg-amber-50" id="live-oil-prices">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-8">
          <h2 className="text-3xl font-bold text-amber-800">Live Oil Prices (West Bengal)</h2>
          <div className="flex items-center justify-center gap-2 mt-3">
            <div className="bg-amber-100 px-4 py-2 rounded-full shadow-md flex items-center clock-container">
              <Clock className="text-amber-600 mr-2" size={20} />
              <p className="text-amber-700 font-medium">{currentTime} (IST)</p>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-4">
          {prices.map((oil, index) => (
            <div
              key={oil.name}
              className="bg-white rounded-lg shadow-md p-6 text-center transform transition-all duration-300 hover:scale-105 hover:shadow-xl price-card"
            >
              <h3 className="text-xl font-bold text-gray-800 mb-2">{oil.name}</h3>
              <p className="text-2xl font-bold text-amber-600">₹{oil.price}/L</p>
              <div className="mt-2 w-12 h-1 bg-amber-400 mx-auto rounded-full"></div>
            </div>
          ))}
        </div>

        {/* Animation keyframes are added to the component's CSS */}

        <div className="mt-10 text-center">
          <button
            onClick={handleViewAllPrices}
            className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md shadow-sm text-white bg-amber-600 hover:bg-amber-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-amber-500 transition-colors duration-200"
          >
            View All Oil Prices
            <ExternalLink className="ml-2" size={18} />
          </button>
        </div>
      </div>
    </section>
  );
};

export default LiveOilPrices;
