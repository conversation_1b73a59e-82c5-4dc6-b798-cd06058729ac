import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Menu, X } from 'lucide-react';

interface NavbarProps {
  isAdminLoggedIn: boolean;
  onLogout: () => void;
  onShowAdminPanel: () => void;
  onShowAdminLogin: () => void;
}

const Navbar: React.FC<NavbarProps> = ({
  isAdminLoggedIn,
  onLogout,
  onShowAdminPanel,
  onShowAdminLogin,
}) => {
  const [isScrolled, setIsScrolled] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 50);
    };
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  return (
    <motion.nav
      initial={{ y: -100 }}
      animate={{ y: 0 }}
      className={`fixed w-full z-50 transition-all duration-300 ${
        isScrolled ? 'bg-white shadow-lg' : 'bg-white/80 backdrop-blur-sm'
      }`}
    >
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 overflow-x-hidden">
          <div className="flex justify-between items-center h-16 md:h-20">
          <div className="flex items-center">
            <div className="flex items-center">
              <motion.img 
                src="/images/logo.png" 
                alt="Jayita Logo" 
                className="h-8 md:h-10 mr-2 md:mr-3"
                whileHover={{ scale: 1.05 }}
              />
              <motion.span 
                className="text-lg md:text-2xl font-extrabold text-yellow-600"
                whileHover={{ scale: 1.05 }}
              >
                JAYITA GROUP
              </motion.span>
            </div>
          </div>
          
          <div className="hidden md:flex items-center space-x-8">
            <NavLink href="#home" className="text-gray-900 hover:text-yellow-600">Home</NavLink>
            <NavLink href="#products" className="text-gray-900 hover:text-yellow-600">Products</NavLink>
            <NavLink href="#product-gallery" className="text-gray-900 hover:text-yellow-600">Gallery</NavLink>
            <NavLink href="#news" className="text-gray-900 hover:text-yellow-600">News</NavLink>
            <NavLink href="#about" className="text-gray-900 hover:text-yellow-600">About</NavLink>
            <NavLink href="#group" className="text-gray-900 hover:text-yellow-600">Group of Companies</NavLink>

            <NavLink href="#contact" className="text-gray-900 hover:text-yellow-600">Contact</NavLink>
            {!isAdminLoggedIn ? (
              <button
                onClick={() => {
                  onShowAdminLogin();
                  setIsMobileMenuOpen(false);
                }}
                className="text-gray-900 hover:text-yellow-600 font-semibold px-3 py-1 border border-yellow-600 rounded hover:bg-yellow-100 transition"
              >
                Admin Login
              </button>
            ) : (
              <>
                <button
                  onClick={() => {
                    onShowAdminPanel();
                    setIsMobileMenuOpen(false);
                  }}
                  className="text-gray-900 hover:text-yellow-600 font-semibold px-3 py-1 border border-yellow-600 rounded hover:bg-yellow-100 transition"
                >
                  Admin Panel
                </button>
                <button
                  onClick={() => {
                    onLogout();
                    setIsMobileMenuOpen(false);
                  }}
                  className="text-gray-900 hover:text-yellow-600 font-semibold px-3 py-1 border border-red-600 rounded hover:bg-red-100 transition"
                >
                  Logout
                </button>
              </>
            )}
          </div>

          <div className="md:hidden">
            <button
              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
              className="text-white-600 hover:text-gray-900"
            >
              {isMobileMenuOpen ? <X size={24} /> : <Menu size={24} />}
            </button>
          </div>
        </div>
      </div>

      {/* Mobile menu */}
      {isMobileMenuOpen && (
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -20 }}
          className="md:hidden bg-white shadow-lg"
        >
          <div className="px-2 pt-2 pb-3 space-y-1">
            <MobileNavLink href="#home" setIsMobileMenuOpen={setIsMobileMenuOpen}>Home</MobileNavLink>
            <MobileNavLink href="#products" setIsMobileMenuOpen={setIsMobileMenuOpen}>Products</MobileNavLink>
            <MobileNavLink href="#product-gallery" setIsMobileMenuOpen={setIsMobileMenuOpen}>Gallery</MobileNavLink>
            <MobileNavLink href="#news" setIsMobileMenuOpen={setIsMobileMenuOpen}>News</MobileNavLink>
            <MobileNavLink href="#about" setIsMobileMenuOpen={setIsMobileMenuOpen}>About</MobileNavLink>
            <MobileNavLink href="#group" setIsMobileMenuOpen={setIsMobileMenuOpen}>Group of Companies</MobileNavLink>

            <MobileNavLink href="#contact" setIsMobileMenuOpen={setIsMobileMenuOpen}>Contact</MobileNavLink>
            {!isAdminLoggedIn ? (
              <button
                onClick={() => {
                  onShowAdminLogin();
                  setIsMobileMenuOpen(false);
                }}
                className="block w-full text-left px-3 py-2 text-gray-900 hover:text-yellow-600 hover:bg-yellow-100 rounded font-semibold"
              >
                Admin Login
              </button>
            ) : (
              <>
                <button
                  onClick={() => {
                    onShowAdminPanel();
                    setIsMobileMenuOpen(false);
                  }}
                  className="block w-full text-left px-3 py-2 text-gray-900 hover:text-yellow-600 hover:bg-yellow-100 rounded font-semibold"
                >
                  Admin Panel
                </button>
                <button
                  onClick={() => {
                    onLogout();
                    setIsMobileMenuOpen(false);
                  }}
                  className="block w-full text-left px-3 py-2 text-red-600 hover:text-red-800 hover:bg-red-100 rounded font-semibold"
                >
                  Logout
                </button>
              </>
            )}
          </div>
        </motion.div>
      )}
    </motion.nav>
  );
};

interface NavLinkProps {
  href: string;
  children: React.ReactNode;
  className?: string;
  setIsMobileMenuOpen?: React.Dispatch<React.SetStateAction<boolean>>;
}

const NavLink = ({ href, children }: NavLinkProps) => {
  const handleClick = (e: React.MouseEvent, href: string) => {
    e.preventDefault();
    const element = document.querySelector(href);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    <motion.a
      href={href}
      className="text-gray-600 hover:text-yellow-600 transition-colors"
      whileHover={{ scale: 1.1 }}
      onClick={(e) => handleClick(e, href)}
    >
      {children}
    </motion.a>
  );
};

const MobileNavLink = ({ href, children, setIsMobileMenuOpen }: NavLinkProps) => {
  const handleClick = () => {
    // Close mobile menu after a small delay to allow navigation
    setTimeout(() => {
      if (setIsMobileMenuOpen) {
        setIsMobileMenuOpen(false);
      }
    }, 300);
  };

  return (
    <motion.a
      href={href}
      className="block px-3 py-2 text-gray-600 hover:text-yellow-600 hover:bg-gray-50 rounded-md"
      whileTap={{ scale: 0.95 }}
      onClick={handleClick}
    >
      {children}
    </motion.a>
  );
};

export default Navbar;
