<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/png" href="/images/logo.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Jayita Edible Oil - Pure & Natural Mustard Oil</title>
    <meta name="description" content="Premium quality mustard oil produced using traditional methods with modern standards. Pure, natural, and healthy cooking oil for your family.">
    <meta name="keywords" content="Mustard Oil, Edible Oil, Pure Mustard Oil, Cooking Oil, Natural Oil, Jayita Group, Kachi Ghani Mustard Oil, Cold Pressed Mustard Oil, Organic Mustard Oil, Traditional Mustard Oil, Healthy Cooking Oil, Indian Cooking Oil, Bengal Mustard Oil, Soyabean Oil, Sunflower Oil, Palm Oil, Vegetable Oil, Kolkata Edible Oil, West Bengal Oil Manufacturer, Premium Cooking Oil, Extra Virgin Mustard Oil, Bulk Edible Oil, Wholesale Mustard Oil, Retail Cooking Oil, Jayita Edible Oil Pvt Ltd">

    <!-- Open Graph / Social Media Meta Tags -->
    <meta property="og:title" content="Jayita Edible Oil - Pure & Natural Mustard Oil">
    <meta property="og:description" content="Premium quality mustard oil produced using traditional methods with modern standards.">
    <meta property="og:image" content="https://www.jayitagroup.com/path-to-your-banner.jpg">
    <meta property="og:url" content="https://www.jayitagroup.com/">
    <meta property="og:type" content="website">

    <!-- Preload critical assets -->
    <link rel="preload" href="/images/background-image.jpg" as="image" fetchpriority="high" />
    <link rel="preload" href="/images/logo.png" as="image" fetchpriority="high" />

    <!-- Performance optimizations -->
    <link rel="dns-prefetch" href="https://fonts.googleapis.com" />
    <link rel="dns-prefetch" href="https://fonts.gstatic.com" />

    <style>
      /* Initial loading state styles */
      body {
        margin: 0;
        padding: 0;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
      }
      #root {
        min-height: 100vh;
        background-color: #fff;
      }
      .loading-indicator {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        display: flex;
        justify-content: center;
        align-items: center;
        background-color: #fff;
        z-index: 9999;
      }
      .loading-spinner {
        width: 50px;
        height: 50px;
        border: 5px solid #f3f3f3;
        border-top: 5px solid #f59e0b;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
    </style>
  </head>
  <body>
    <div id="root">
      <div class="loading-indicator">
        <div class="loading-spinner"></div>
      </div>
    </div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>