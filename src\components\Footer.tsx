import React from 'react';
import { motion } from 'framer-motion';
import { Phone, Mail, MapPin } from 'lucide-react';

const Footer = () => {
  return (
    <footer className="bg-gray-900 text-white" id="contact">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            <h3 className="text-2xl font-bold mb-4">Jayita Group</h3>
            <p className="text-gray-400">
              Bringing pure and natural products to your kitchen .
            </p>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            <h4 className="text-xl font-semibold mb-4">Contact Us</h4>
            <div className="space-y-3">
              <div className="flex items-center">
                <Phone size={20} className="mr-2 text-yellow-500" />
                <span>+91 9123875815</span>
              </div>
              <div className="flex items-center">
                <Mail size={20} className="mr-2 text-yellow-500" />
                <span><EMAIL></span>
              </div>
              <div className="flex items-center">
                <MapPin size={20} className="mr-2 text-yellow-500" />
                <span>MANI CASADONA, 6ES8B, 6TH FLOOR, EAST BLOCK, STREET NO 372, NEW TOWN, KOLKATA -700156</span>
              </div>
            </div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.4 }}
          >
            <h4 className="text-xl font-semibold mb-4">Quick Links</h4>
            <ul className="space-y-2">
              <li>
                <a href="#home" className="hover:text-yellow-500 transition-colors">
                  Home
                </a>
              </li>
              <li>
                <a href="#products" className="hover:text-yellow-500 transition-colors">
                  Products
                </a>
              </li>
              <li>
                <a href="#about" className="hover:text-yellow-500 transition-colors">
                  About Us
                </a>
              </li>
              <li>
                <a href="#contact" className="hover:text-yellow-500 transition-colors">
                  Contact
                </a>
              </li>
            </ul>
          </motion.div>
        </div>

        <motion.div
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          transition={{ duration: 0.5, delay: 0.6 }}
          className="mt-12 pt-8 border-t border-gray-800 text-center text-gray-400"
        >
          <p>&copy; {new Date().getFullYear()} Jayita Edible Oil. All rights reserved.</p>
          <p className="mt-2">
            Site developed & maintained by{' '}
            <a 
              href="/triunity-solutions.html" 
              className="text-yellow-500 hover:text-yellow-400 transition-colors"
            >
              TRIUNITY SOLUTIONS
            </a>
            , contact no: +91 9547773393
          </p>
        </motion.div>
      </div>
    </footer>
  );
};

export default Footer;