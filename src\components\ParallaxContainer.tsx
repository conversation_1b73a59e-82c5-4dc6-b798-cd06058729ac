import React, { useState, useEffect, ReactNode } from 'react';
import { motion, useMotionValue, useSpring, useTransform } from 'framer-motion';

interface ParallaxContainerProps {
  children: ReactNode;
  depth?: number;
  className?: string;
}

const ParallaxContainer: React.FC<ParallaxContainerProps> = ({
  children,
  depth = 20,
  className = '',
}) => {
  const [windowSize, setWindowSize] = useState({
    width: typeof window !== 'undefined' ? window.innerWidth : 0,
    height: typeof window !== 'undefined' ? window.innerHeight : 0,
  });

  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });

  // Update window dimensions when resized
  useEffect(() => {
    const handleResize = () => {
      setWindowSize({
        width: window.innerWidth,
        height: window.innerHeight,
      });
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Track mouse position
  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      // Calculate mouse position relative to the center of the screen
      const x = (e.clientX - windowSize.width / 2) / (windowSize.width / 2);
      const y = (e.clientY - windowSize.height / 2) / (windowSize.height / 2);
      
      setMousePosition({ x, y });
    };

    window.addEventListener('mousemove', handleMouseMove);
    return () => window.removeEventListener('mousemove', handleMouseMove);
  }, [windowSize]);

  // Create smooth motion values
  const springConfig = { damping: 25, stiffness: 100 };
  
  const xMotion = useMotionValue(0);
  const yMotion = useMotionValue(0);
  
  const xSpring = useSpring(xMotion, springConfig);
  const ySpring = useSpring(yMotion, springConfig);

  // Update motion values when mouse position changes
  useEffect(() => {
    xMotion.set(mousePosition.x * depth);
    yMotion.set(mousePosition.y * depth);
  }, [mousePosition, depth, xMotion, yMotion]);

  // Transform for rotation effect
  const rotateX = useTransform(ySpring, [-depth, depth], [5, -5]);
  const rotateY = useTransform(xSpring, [-depth, depth], [-5, 5]);

  return (
    <motion.div
      className={`relative ${className}`}
      style={{
        x: xSpring,
        y: ySpring,
        rotateX,
        rotateY,
        perspective: 1000,
        transformStyle: 'preserve-3d',
      }}
    >
      {children}
    </motion.div>
  );
};

export default ParallaxContainer;