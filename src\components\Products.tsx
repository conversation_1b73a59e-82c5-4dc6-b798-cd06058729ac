import React from 'react';
import { motion } from 'framer-motion';
import { cardFlip, hoverScale, glowEffect, scrollReveal } from '../utils/motion';

const Products = () => {

  // EDIT HERE: Update product details and images
  const products = [
    {
      id: "soyabean-oil",
      name: "Organic Soyabean Oil",
      image: "/images/product_images/Soyabean_Oil_15_Kg_Tin.jpg",
      description: "Certified Healthy Soyabean Oil",
      sizes: ["500 ml", "15 kg"],
      badge: "Premium"
    },
    {
      id: "kachi-ghani-mustard-oil",
      name: "Kachi Ghani Mustard Oil",
      image: "/images/product_images/Kachi_Ghani_Mustard_Oil_15_kg_Tin.jpg",
      description: "First press, premium quality",
      sizes: ["500ml", "15 kg"],
      badge: "Traditional"
    }
  ];

  const upcomingProducts = [
    {
      name: "Organic Coconut Oil",
      description: "Cold-pressed virgin coconut oil",
      comingSoon: true
    },
    {
      name: "Olive Oil Blend",
      description: "Mediterranean-inspired olive oil blend",
      comingSoon: true
    }
  ];

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.15
      }
    }
  };

  return (
    <section className="py-20 bg-white" id="products">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5 }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl font-bold text-gray-900 mb-4">Our Products</h2>
          <div className="w-24 h-1 bg-amber-500 mx-auto mb-6 rounded-full"></div>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Discover our range of premium oils crafted with care for discerning tastes
          </p>
          <motion.a 
            href="#product-gallery" 
            className="inline-block mt-6 py-3 px-6 bg-gradient-to-r from-amber-500 to-yellow-500 text-white rounded-full font-medium shadow-lg hover:shadow-xl transition-all"
            whileHover={{ 
              scale: 1.05,
              boxShadow: "0 10px 25px -5px rgba(245, 158, 11, 0.4)"
            }}
            whileTap={{ scale: 0.95 }}
          >
            View Full Gallery
          </motion.a>
        </motion.div>

        {/* Enhanced Existing Products Section */}
        <motion.div
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5 }}
          className="mb-20"
        >
          <h3 className="text-2xl font-bold text-gray-800 mb-8 text-center">
            <span className="relative inline-block">
              Existing Products
              <motion.span 
                className="absolute -bottom-2 left-0 right-0 h-1 bg-amber-400 rounded-full"
                initial={{ scaleX: 0 }}
                whileInView={{ scaleX: 1 }}
                viewport={{ once: true }}
                transition={{ duration: 0.8, delay: 0.2 }}
              />
            </span>
          </h3>
          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, amount: 0.1 }}
            variants={containerVariants}
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8"
          >
            {products.map((product, index) => (
              <motion.div
                key={index}
                variants={scrollReveal}
                whileHover={{ 
                  y: -10,
                  boxShadow: "0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)",
                  transition: { type: 'spring', stiffness: 300 }
                }}
                className="bg-white rounded-xl overflow-hidden shadow-lg border border-gray-100 relative"
              >
                {product.badge && (
                  <motion.div 
                    className="absolute top-4 right-4 z-10 bg-amber-500 text-white text-xs font-bold px-3 py-1 rounded-full"
                    initial={{ opacity: 0, scale: 0.8 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ delay: 0.2 + index * 0.1 }}
                  >
                    {product.badge}
                  </motion.div>
                )}
                <motion.div
                  whileHover={{ scale: 1.05 }}
                  transition={{ type: 'spring', stiffness: 400 }}
                  className="relative h-64 overflow-hidden"
                >
                  <div className="absolute inset-0 bg-gradient-to-t from-black/30 to-transparent z-10" />
                  <motion.img
                    src={product.image}
                    alt={product.name}
                    className="w-full h-full object-cover"
                    initial={{ scale: 1.2, filter: 'blur(5px)' }}
                    animate={{ scale: 1, filter: 'blur(0px)' }}
                    transition={{ duration: 0.5, delay: 0.1 * index }}
                  />
                </motion.div>
                <div className="p-6">
                  <h3 className="text-xl font-semibold mb-2 text-gray-800">{product.name}</h3>
                  <p className="text-gray-600 mb-4">{product.description}</p>
                  <div className="flex flex-wrap gap-2 mb-4">
                    {product.sizes.map((size, i) => (
                      <motion.span
                        key={i}
                        whileHover={{ 
                          scale: 1.1,
                          backgroundColor: '#fbbf24',
                          color: '#ffffff'
                        }}
                        whileTap={{ scale: 0.95 }}
                        className="px-3 py-1 bg-amber-100 text-amber-800 rounded-full text-sm font-medium cursor-pointer transition-colors"
                      >
                        {size}
                      </motion.span>
                    ))}
                  </div>
                  <motion.a
                    href={`/product-details/${product.id}.html`}
                    whileHover={{ 
                      scale: 1.03,
                      boxShadow: "0 4px 12px rgba(245, 158, 11, 0.3)"
                    }}
                    whileTap={{ scale: 0.97 }}
                    className="inline-block w-full text-center py-3 px-4 bg-gradient-to-r from-amber-500 to-yellow-500 text-white rounded-lg font-medium shadow-md transition-all"
                  >
                    View Details
                  </motion.a>
                </div>
              </motion.div>
            ))}
          </motion.div>
        </motion.div>

        {/* Enhanced Upcoming Products Section */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.7 }}
        >
          <h3 className="text-2xl font-bold text-gray-800 mb-8 text-center">
            <span className="relative inline-block">
              Upcoming Products
              <motion.span 
                className="absolute -bottom-2 left-0 right-0 h-1 bg-amber-400 rounded-full"
                initial={{ scaleX: 0 }}
                whileInView={{ scaleX: 1 }}
                viewport={{ once: true }}
                transition={{ duration: 0.8, delay: 0.2 }}
              />
            </span>
          </h3>
          <div className="bg-gradient-to-br from-amber-50 to-yellow-50 rounded-xl p-10 shadow-inner border border-amber-100">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              {upcomingProducts.map((product, index) => (
                <motion.div 
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ delay: 0.2 * index, duration: 0.5 }}
                  className="bg-white/80 backdrop-blur-sm p-6 rounded-lg shadow-md border border-amber-100"
                >
                  <div className="flex items-center mb-4">
                    <div className="w-12 h-12 rounded-full bg-amber-100 flex items-center justify-center mr-4">
                      <motion.div 
                        className="w-8 h-8 rounded-full bg-amber-400"
                        animate={{ 
                          boxShadow: ['0 0 0 0 rgba(245, 158, 11, 0.4)', '0 0 0 10px rgba(245, 158, 11, 0)', '0 0 0 0 rgba(245, 158, 11, 0)'],
                        }}
                        transition={{ 
                          duration: 2,
                          repeat: Infinity,
                          repeatDelay: 1
                        }}
                      />
                    </div>
                    <h4 className="text-xl font-semibold text-gray-800">{product.name}</h4>
                  </div>
                  <p className="text-gray-600 mb-4">{product.description}</p>
                  <div className="flex items-center text-amber-600 font-medium">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    Coming Soon
                  </div>
                </motion.div>
              ))}
            </div>
            <motion.div 
              className="mt-8 text-center"
              initial={{ opacity: 0 }}
              whileInView={{ opacity: 1 }}
              viewport={{ once: true }}
              transition={{ delay: 0.5 }}
            >
              <p className="text-gray-600">Stay tuned for our exciting new additions to our product line</p>
              <motion.button
                className="mt-4 px-6 py-2 bg-white text-amber-600 border border-amber-300 rounded-full font-medium hover:bg-amber-50 transition-colors"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                Notify Me When Available
              </motion.button>
            </motion.div>
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default Products;