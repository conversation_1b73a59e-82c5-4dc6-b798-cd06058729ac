import React, { useEffect, useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useInView } from 'react-intersection-observer';
import { Droplets, Leaf, Shield, Sun, Award } from 'lucide-react';
import Navbar from './components/Navbar';
import Hero from './components/Hero';
import About from './components/About';
import Features from './components/Features';
import Products from './components/Products';
import ProductGallery from './components/ProductGallery';
import GroupCompanies from './components/GroupCompanies';

import LiveOilPrices from './components/LiveOilPrices';
import WavesDivider from './components/WavesDivider';
import News from './components/News';
import Footer from './components/Footer';
// Import cursor fix CSS
import './components/cursor-fix.css';

interface OilPrice {
  name: string;
  price: string;
}

const initialPrices: OilPrice[] = [
  { name: 'Sunflower Oil', price: '145.3' },
  { name: 'Mustard Oil', price: '153.3' },
  { name: 'Kachi Ghani Mustard Oil', price: '157.3' },
  { name: 'Soybean Oil', price: '136.8' },
  { name: 'Palm Oil', price: '131.3' },
  { name: 'Cooking Oil', price: '141.3' }
];

function App() {
  const [prices] = useState<OilPrice[]>(initialPrices);

  // Scroll to top on page load/refresh
  useEffect(() => {
    const handleBeforeUnload = () => {
      window.scrollTo(0, 0);
    };
    window.addEventListener('beforeunload', handleBeforeUnload);
    window.scrollTo(0, 0);
    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  }, []);

  useEffect(() => {
    // Throttle function to limit how often the scroll handler runs
    const throttle = (func: Function, limit: number) => {
      let inThrottle: boolean;
      return function() {
        if (!inThrottle) {
          func();
          inThrottle = true;
          setTimeout(() => inThrottle = false, limit);
        }
      };
    };
    const handleScroll = throttle(() => {
      const sections = ['home', 'news', 'products', 'product-gallery', 'about', 'group', 'contact'];
      sections.find(section => {
        const element = document.getElementById(section);
        if (element) {
          const rect = element.getBoundingClientRect();
          return rect.top <= 100 && rect.bottom >= 100;
        }
        return false;
      });
      // No state update needed
    }, 100);
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  // Provide dummy props to Navbar to satisfy its required props
  const dummy = () => {};

  return (
    <div className="min-h-screen bg-white">
      {/* Navigation */}
      <Navbar
        isAdminLoggedIn={false}
        onLogout={dummy}
        onShowAdminPanel={dummy}
        onShowAdminLogin={dummy}
      />
      {/* Hero Section */}
      <div id="home">
        <Hero />
      </div>
      <WavesDivider color="gold" height={100} />
      {/* About Section */}
      <About />
      <WavesDivider color="white" reversed={true} height={100} />
      {/* Features Section */}
      <div>
        <Features />
      </div>
      <WavesDivider color="amber" height={100} />
      {/* Products Section */}
      <div id="products">
        <Products />
      </div>
      <WavesDivider color="gold" reversed={true} height={100} />
      {/* Product Gallery Section */}
      <div id="product-gallery">
        <ProductGallery />
      </div>
      <WavesDivider color="white" height={100} />
      {/* News Section */}
      <div id="news">
        <News />
      </div>
      <WavesDivider color="amber" reversed={true} height={100} />
      {/* Group Companies Section */}
      <div id="group">
        <GroupCompanies />
      </div>
      <WavesDivider color="gold" height={100} />
      {/* Live Oil Prices Section */}
      <div id="oil-prices">
        <LiveOilPrices prices={prices} />
      </div>
      <WavesDivider color="white" reversed={true} height={100} />
      {/* Footer Section */}
      <Footer />
    </div>
  );
}

export default App;

