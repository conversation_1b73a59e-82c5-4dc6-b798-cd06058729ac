import React, { useState } from 'react';
import { motion } from 'framer-motion';

// Define the product data structure
interface Product {
  id: string;
  name: string;
  image: string;
  description: string;
  category: string;
}

const ProductGallery = () => {
  // Product data based on the images in public/images/product_images
  const products: Product[] = [
    // Detergent Products
    {
      id: "dr-cleanmaster-1kg",
      name: "Dr. Cleanmaster (1kg Pack)",
      image: "/images/product_images/dr_cleanmaster_1kg.jpg",
      description: "Premium cleaning powder for all your household cleaning needs",
      category: "detergent"
    },
    {
      id: "dr-cleanmaster-800gm",
      name: "Dr. Cleanmaster (800gm Pack)",
      image: "/images/product_images/dr_cleanmaster_800gm.jpg",
      description: "Versatile cleaning powder for everyday household use",
      category: "detergent"
    },
    {
      id: "dr-cleanmaster-500gm",
      name: "Dr. Cleanmaster (500gm Pack)",
      image: "/images/product_images/dr_cleanmaster_500gm.jpg",
      description: "Compact cleaning solution for small households",
      category: "detergent"
    },
    {
      id: "dr-cleanmaster-120gm",
      name: "Dr. Cleanmaster (120gm Pack)",
      image: "/images/product_images/dr_cleanmaster_120gm.jpg",
      description: "Travel-sized cleaning powder for on-the-go convenience",
      category: "detergent"
    },
    {
      id: "dishwash-bar",
      name: "Dishwash Bar",
      image: "/images/product_images/Dishwash_bar.jpg",
      description: "Powerful dishwashing bar for effective grease removal and sparkling clean utensils",
      category: "detergent"
    },
    {
      id: "detergent-cake-250gm",
      name: "Detergent Cake (250gm)",
      image: "/images/product_images/detergent_cake_250gm.jpg",
      description: "Compact detergent cake for effective stain removal and fresh fragrance",
      category: "detergent"
    },

    // Oil Products
    {
      id: "kachi-ghani-mustard-oil-bottle",
      name: "Kachi Ghani Mustard Oil (500ml Bottle)",
      image: "/images/product_images/Kachin_Ghani_Mustard_Oil_500_ml_bottle.jpg",
      description: "Pure Kachi Ghani mustard oil in convenient bottle packaging",
      category: "oil"
    },

    {
      id: "kachi-ghani-mustard-oil-tin",
      name: "Kachi Ghani Mustard Oil (15kg Tin)",
      image: "/images/product_images/Kachi_Ghani_Mustard_Oil_15_kg_Tin.jpg",
      description: "Bulk packaging of premium Kachi Ghani mustard oil",
      category: "oil"
    },
    {
      id: "kachi-ghani-mustard-oil-1-liter",
      name: "Kachi Ghani Mustard Oil (1 Liter Bottle)",
      image: "/images/product_images/Kachi_Ghani_Mustard_Oil_1_liter_bottle.jpg",
      description: "Premium Kachi Ghani mustard oil in family-sized 1 liter bottle",
      category: "oil"
    },
    {
      id: "kachi-mustard-oil-200ml",
      name: "Kachi Mustard Oil (200ml Bottle)",
      image: "/images/product_images/kachi_Mustard_oil_200_ml_bottle.jpg",
      description: "Pure Kachi mustard oil in convenient small bottle packaging",
      category: "oil"
    },

    {
      id: "mustard-oil-1-liter",
      name: "Mustard Oil (1 Liter Bottle)",
      image: "/images/product_images/Mustard_oil_1_liter_bottle.jpg",
      description: "Pure mustard oil in family-sized 1 liter bottle",
      category: "oil"
    },
    {
      id: "mustard-oil-500ml",
      name: "Mustard Oil (500ml Bottle)",
      image: "/images/product_images/Mustard_Oil_500_ml_bottle.jpg",
      description: "Pure mustard oil in convenient medium-sized bottle",
      category: "oil"
    },
    {
      id: "mustard-oil-200ml",
      name: "Mustard Oil (200ml Bottle)",
      image: "/images/product_images/Mustard_oil_200_ml_bottle.jpg",
      description: "Pure mustard oil in convenient small bottle packaging",
      category: "oil"
    },

    {
      id: "palm-oil-tin",
      name: "Palm Oil (15kg Tin)",
      image: "/images/product_images/Pam_oil_15_kg_tin.jpg",
      description: "Premium quality palm oil in bulk packaging",
      category: "oil"
    },
    {
      id: "cooking-oil-tin",
      name: "Cooking Oil (15kg Tin)",
      image: "/images/product_images/Cooking_oil_15_kg_tin.jpg",
      description: "Versatile cooking oil for all your culinary needs",
      category: "oil"
    },
    {
      id: "soyabean-oil-bottle",
      name: "Soyabean Oil (500ml Bottle)",
      image: "/images/product_images/Soyabean_oil_500_ml_bottle.jpg",
      description: "Pure soyabean oil in convenient bottle packaging",
      category: "oil"
    },
    {
      id: "soyabean-oil-tin",
      name: "Soyabean Oil (15kg Tin)",
      image: "/images/product_images/Soyabean_Oil_15_Kg_Tin.jpg",
      description: "Bulk packaging of premium soyabean oil",
      category: "oil"
    },
    {
      id: "soyabean-oil-200ml",
      name: "Soyabean Oil (200ml Bottle)",
      image: "/images/product_images/Soyabean_oil_200_ml_bottle.jpg",
      description: "Pure soyabean oil in small bottle packaging",
      category: "oil"
    },
    {
      id: "soyabean-oil-1-liter",
      name: "Soyabean Oil (1 Liter Bottle)",
      image: "/images/product_images/Soyabean_oil_1_liter_bottle.jpg",
      description: "Pure soyabean oil in family-sized 1 liter bottle",
      category: "oil"
    },


  ];

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        type: 'spring',
        stiffness: 100
      }
    }
  };

  // Filter products by category
  const detergentProducts = products.filter(product => product.category === 'detergent');
  const oilProducts = products.filter(product => product.category === 'oil');

  // Product card component for reuse
  const ProductCard = ({ product }: { product: Product }) => (
    <motion.div
      variants={itemVariants}
      whileHover={{
        y: -10,
        transition: { type: 'spring', stiffness: 300 }
      }}
      className="bg-white rounded-lg overflow-hidden shadow-md hover:shadow-xl transition-all"
    >
      <a href={`/product-details/${product.id}.html`} className="block">
        <div className="relative h-48 overflow-hidden">
          <motion.img
            whileHover={{ scale: 1.05 }}
            transition={{ type: 'spring', stiffness: 400 }}
            src={product.image}
            alt={product.name}
            className="w-full h-full object-cover"
            onError={(e) => {
              const target = e.target as HTMLImageElement;
              target.onerror = null;
              target.src = '/images/logo.png'; // Fallback to logo if image fails to load
            }}
          />
        </div>
        <div className="p-4">
          <h3 className="text-lg font-semibold mb-2 text-gray-900">{product.name}</h3>
          <p className="text-sm text-gray-600 line-clamp-2">{product.description}</p>
          <motion.div
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            className="mt-4 text-center py-2 px-4 bg-yellow-500 text-white rounded-md font-medium hover:bg-yellow-600 transition-colors"
          >
            View Details
          </motion.div>
        </div>
      </a>
    </motion.div>
  );

  return (
    <section className="py-20 bg-white" id="product-gallery">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5 }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl font-bold text-gray-900 mb-4">Product Gallery</h2>
          <p className="text-xl text-gray-600">Explore our complete range of premium products</p>
        </motion.div>

        {/* Detergent Products Section */}
        <div className="mb-16">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5 }}
            className="text-center mb-8"
          >
            <h3 className="text-2xl font-bold text-gray-900 mb-2">Detergent Products</h3>
            <div className="w-24 h-1 bg-amber-500 mx-auto mb-4 rounded-full"></div>
            <p className="text-gray-600">Our premium cleaning solutions for your home</p>
          </motion.div>

          <motion.div
            variants={containerVariants}
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, amount: 0.1 }}
            className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6 px-4"
          >
            {detergentProducts.map((product, index) => (
              <ProductCard key={index} product={product} />
            ))}
          </motion.div>
        </div>

        {/* Oil Products Section */}
        <div>
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5, delay: 0.2 }}
            className="text-center mb-8"
          >
            <h3 className="text-2xl font-bold text-gray-900 mb-2">Edible Oil Products</h3>
            <div className="w-24 h-1 bg-amber-500 mx-auto mb-4 rounded-full"></div>
            <p className="text-gray-600">Our range of premium quality cooking oils</p>
          </motion.div>

          <motion.div
            variants={containerVariants}
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, amount: 0.1 }}
            className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6 px-4"
          >
            {oilProducts.slice(0, 8).map((product, index) => (
              <ProductCard key={index} product={product} />
            ))}
          </motion.div>

          {oilProducts.length > 8 && (
            <div className="text-center mt-8">
              <motion.a
                href="/factory-details/jayita-oil.html"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="inline-block py-3 px-6 bg-gradient-to-r from-amber-500 to-yellow-500 text-white rounded-full font-medium shadow-lg hover:shadow-xl transition-all"
              >
                View All Oil Products
              </motion.a>
            </div>
          )}
        </div>
      </div>
    </section>
  );
};

export default ProductGallery;