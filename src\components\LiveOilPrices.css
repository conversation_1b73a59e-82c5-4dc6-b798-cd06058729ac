@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.price-card {
  animation: fadeInUp 0.5s ease-out forwards;
}

.price-card:nth-child(1) { animation-delay: 0.1s; }
.price-card:nth-child(2) { animation-delay: 0.2s; }
.price-card:nth-child(3) { animation-delay: 0.3s; }
.price-card:nth-child(4) { animation-delay: 0.4s; }
.price-card:nth-child(5) { animation-delay: 0.5s; }
.price-card:nth-child(6) { animation-delay: 0.6s; }

.clock-container {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(217, 119, 6, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(217, 119, 6, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(217, 119, 6, 0);
  }
}