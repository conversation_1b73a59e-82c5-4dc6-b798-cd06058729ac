import React, { useEffect, useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useInView } from 'react-intersection-observer';
import { Droplets, Leaf, Shield, Sun, Award } from 'lucide-react';
import Navbar from './components/Navbar';
import Hero from './components/Hero';
import About from './components/About';
import Features from './components/Features';
import Products from './components/Products';
import GroupCompanies from './components/GroupCompanies';
import Management from './components/Management';
import OilPrices from './components/OilPrices';
import WavesDivider from './components/WavesDivider';
import News from './components/News';
import Footer from './components/Footer';

function App() {
  const [activeSection, setActiveSection] = useState('home');

  useEffect(() => {
    const handleScroll = () => {
      const sections = ['home', 'news', 'products', 'about', 'management', 'group', 'contact'];
      const currentSection = sections.find(section => {
        const element = document.getElementById(section);
        if (element) {
          const rect = element.getBoundingClientRect();
          return rect.top <= 100 && rect.bottom >= 100;
        }
        return false;
      });
      if (currentSection) {
        setActiveSection(currentSection);
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  return (
    <div className="min-h-screen bg-white">
      {/* Navigation */}
      <Navbar />

      {/* Hero Section */}
      <AnimatePresence mode="wait">
        <motion.div
          key="hero"
          initial={{ opacity: 1 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.5 }}
        >
        <Hero />
        </motion.div>
      </AnimatePresence>

      <WavesDivider />

      {/* About Section */}
      <About />

      <WavesDivider />

      {/* Features Section */}
      <AnimatePresence mode="wait">
        <motion.div
          key="features"
          initial={{ opacity: 0 }}
          animate={{ opacity: activeSection === 'about' ? 1 : 0.3 }}
          transition={{ duration: 0.5 }}
        >
          <Features />
        </motion.div>
      </AnimatePresence>

      <WavesDivider />

      {/* Products Section */}
      <AnimatePresence mode="wait">
        <motion.div
          key="products"
          initial={{ opacity: 0 }}
          animate={{ opacity: activeSection === 'products' ? 1 : 0.3 }}
          transition={{ duration: 0.5 }}
        >
          <Products />
        </motion.div>
      </AnimatePresence>

      <WavesDivider />

      {/* Group Companies Section */}
      <AnimatePresence mode="wait">
        <motion.div
          key="group"
          initial={{ opacity: 0 }}
          animate={{ opacity: activeSection === 'group' ? 1 : 0.3 }}
          transition={{ duration: 0.5 }}
        >
          <GroupCompanies />
        </motion.div>
      </AnimatePresence>

      <WavesDivider />

      {/* Management Section */}
      <AnimatePresence mode="wait">
        <motion.div
          key="management"
          initial={{ opacity: 0 }}
          animate={{ opacity: activeSection === 'management' ? 1 : 0.3 }}
          transition={{ duration: 0.5 }}
        >
        <Management />
        <OilPrices />
        </motion.div>
      </AnimatePresence>

      <WavesDivider />

      {/* Footer Section */}
      <Footer />
    </div>
  );
}

export default App;
