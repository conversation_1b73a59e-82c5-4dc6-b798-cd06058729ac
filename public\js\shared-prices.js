/**
 * Shared Oil Prices Module
 * 
 * This module provides functions to get and update oil prices across different pages.
 * It uses localStorage to persist the prices between page loads and
 * dispatches custom events to notify other pages of price changes.
 */

// Storage key for oil prices in localStorage
const STORAGE_KEY = 'jayita_oil_prices';

// Default prices if none are stored
const DEFAULT_PRICES = {
    "Sunflower Oil": "145.3",
    "Mustard Oil": "153.3",
    "Kachi Ghani Mustard Oil": "157.3",
    "Soybean Oil": "136.8",
    "Palm Oil": "131.3",
    "Cooking Oil": "141.3"
};

/**
 * Get the current oil prices from localStorage
 * @returns {Object} Object containing oil prices
 */
export function getSharedPrices() {
    try {
        const storedPrices = localStorage.getItem(STORAGE_KEY);
        if (storedPrices) {
            return JSON.parse(storedPrices);
        }
    } catch (error) {
        console.error('Error retrieving oil prices from localStorage:', error);
    }
    
    // Return default prices if none are stored or there was an error
    return DEFAULT_PRICES;
}

/**
 * Update the oil prices in localStorage and notify other pages
 * @param {Object} priceMap - Object containing updated oil prices
 */
export function updateSharedPrices(priceMap) {
    try {
        // Get current prices
        const currentPrices = getSharedPrices();
        
        // Merge with new prices (only update values that are provided)
        const updatedPrices = { ...currentPrices };
        
        for (const [key, value] of Object.entries(priceMap)) {
            if (value !== null && value !== undefined) {
                updatedPrices[key] = value;
            }
        }
        
        // Save to localStorage
        localStorage.setItem(STORAGE_KEY, JSON.stringify(updatedPrices));
        
        // Dispatch event to notify other pages
        const event = new CustomEvent('oilPricesUpdated', { 
            detail: updatedPrices 
        });
        window.dispatchEvent(event);
        
        return true;
    } catch (error) {
        console.error('Error updating oil prices in localStorage:', error);
        return false;
    }
}
