
import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
// Removing the splitVendorChunkPlugin import since we'll use manualChunks instead

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [
    react(),
    // Removed splitVendorChunkPlugin
  ],
  server: {
    host: '0.0.0.0', // This makes the dev server accessible on LAN
    port: 5173,       // Or any port you prefer
  },
  build: {
    minify: 'terser',
    terserOptions: {
      compress: {
        drop_console: true,
        drop_debugger: true
      }
    },
    rollupOptions: {
      output: {
        manualChunks: (id) => {
          // Function form of manualChunks
          if (id.includes('node_modules')) {
            if (id.includes('react') || id.includes('react-dom')) {
              return 'react-vendor';
            }
            if (id.includes('framer-motion')) {
              return 'framer-motion';
            }
            if (id.includes('lucide-react')) {
              return 'ui-components';
            }
            // Default vendor chunk for other node_modules
            return 'vendor';
          }
        }
      }
    },
    chunkSizeWarningLimit: 1000
  },
  optimizeDeps: {
    include: ['react', 'react-dom', 'framer-motion'],
  },
});
