import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import './FuturisticCursor.css';

interface CursorProps {
  color?: string;
}

const FuturisticCursor: React.FC<CursorProps> = ({ color = 'rgba(245, 158, 11, 0.7)' }) => {
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const [isPointer, setIsPointer] = useState(false);
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    // Only show cursor on desktop devices
    const isMobile = window.matchMedia('(max-width: 768px)').matches;
    setIsVisible(!isMobile);

    const handleMouseMove = (e: MouseEvent) => {
      setMousePosition({ x: e.clientX, y: e.clientY });
      
      // Check if cursor is over a clickable element
      const target = e.target as HTMLElement;
      const isClickable = 
        target.tagName === 'BUTTON' || 
        target.tagName === 'A' || 
        target.onclick || 
        window.getComputedStyle(target).cursor === 'pointer';
      
      setIsPointer(isClickable);
    };

    window.addEventListener('mousemove', handleMouseMove);
    return () => window.removeEventListener('mousemove', handleMouseMove);
  }, []);

  if (!isVisible) return null;

  return (
    <>
      {/* Main cursor */}
      <motion.div
        className="fixed top-0 left-0 w-6 h-6 rounded-full pointer-events-none z-50 mix-blend-difference"
        style={{
          backgroundColor: color,
          x: mousePosition.x - 12,
          y: mousePosition.y - 12,
        }}
        animate={{
          scale: isPointer ? 1.5 : 1,
          opacity: 0.7,
        }}
        transition={{
          type: 'spring',
          stiffness: 500,
          damping: 28,
          mass: 0.5,
        }}
      />

      {/* Trailing effect */}
      <motion.div
        className="fixed top-0 left-0 w-40 h-40 rounded-full pointer-events-none z-40"
        style={{
          x: mousePosition.x - 80,
          y: mousePosition.y - 80,
          background: `radial-gradient(circle, ${color} 0%, rgba(255,255,255,0) 70%)`,
        }}
        animate={{
          scale: isPointer ? 1.2 : 1,
          opacity: isPointer ? 0.15 : 0.07,
        }}
        transition={{
          type: 'spring',
          stiffness: 300,
          damping: 30,
          mass: 0.8,
        }}
      />

      {/* Subtle outer glow */}
      <motion.div
        className="fixed top-0 left-0 w-16 h-16 rounded-full pointer-events-none z-40 blur-sm"
        style={{
          backgroundColor: color,
          x: mousePosition.x - 32,
          y: mousePosition.y - 32,
        }}
        animate={{
          scale: isPointer ? 1.3 : 1,
          opacity: isPointer ? 0.2 : 0.1,
        }}
        transition={{
          type: 'spring',
          stiffness: 400,
          damping: 25,
          mass: 0.6,
          delay: 0.02,
        }}
      />

      {/* Particle trail effect */}
      {isPointer && [...Array(5)].map((_, i) => (
        <motion.div
          key={i}
          className="fixed top-0 left-0 w-3 h-3 rounded-full pointer-events-none z-30"
          style={{
            backgroundColor: color,
            x: mousePosition.x - 6,
            y: mousePosition.y - 6,
          }}
          initial={{ opacity: 0.7, scale: 0.8 }}
          animate={{
            opacity: 0,
            scale: 0.2,
            x: mousePosition.x - 6 + (Math.random() * 40 - 20),
            y: mousePosition.y - 6 + (Math.random() * 40 - 20),
          }}
          transition={{
            duration: 0.8,
            ease: "easeOut",
            delay: i * 0.05,
          }}
        />
      ))}
    </>
  );
};

export default FuturisticCursor;