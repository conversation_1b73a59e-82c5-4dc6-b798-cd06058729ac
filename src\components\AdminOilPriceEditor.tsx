import React, { useState, useEffect } from 'react';

interface OilPrice {
  name: string;
  price: string;
}

interface AdminOilPriceEditorProps {
  prices: OilPrice[];
  setPrices: React.Dispatch<React.SetStateAction<OilPrice[]>>;
}

const AdminOilPriceEditor: React.FC<AdminOilPriceEditorProps> = ({ prices, setPrices }) => {
  const [editedPrices, setEditedPrices] = useState<OilPrice[]>(prices);

  // Update local state when props change
  useEffect(() => {
    setEditedPrices(prices);
  }, [prices]);

  const handlePriceChange = (index: number, newPrice: string) => {
    const updatedPrices = [...editedPrices];
    updatedPrices[index] = { ...updatedPrices[index], price: newPrice };
    setEditedPrices(updatedPrices);
  };

  const handleSave = () => {
    setPrices(editedPrices);
    alert('Oil prices updated successfully!');
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        <h2 className="text-2xl font-bold text-amber-800 mb-6">Update Oil Prices</h2>
        
        <div className="space-y-4">
          {editedPrices.map((oil, index) => (
            <div key={index} className="flex items-center justify-between p-3 bg-amber-50 rounded-lg">
              <span className="font-semibold text-gray-800">{oil.name}</span>
              <div className="flex items-center">
                <span className="text-amber-600 mr-2">₹</span>
                <input
                  type="number"
                  step="0.1"
                  value={oil.price}
                  onChange={(e) => handlePriceChange(index, e.target.value)}
                  className="border border-amber-300 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-amber-500 w-24"
                />
                <span className="text-amber-600 ml-2">/L</span>
              </div>
            </div>
          ))}
        </div>
        
        <div className="mt-8 flex justify-end space-x-4">
          <button
            onClick={handleSave}
            className="px-6 py-2 bg-amber-600 text-white rounded-md hover:bg-amber-700 transition-colors focus:outline-none focus:ring-2 focus:ring-amber-500 focus:ring-offset-2"
          >
            Save Changes
          </button>
        </div>
      </div>
    </div>
  );
};

export default AdminOilPriceEditor;