import React from 'react';
import { motion } from 'framer-motion';
import { fluidWave, hoverScale } from '../utils/motion';

interface WavesDividerProps {
  height?: number;
  waveCount?: number;
  particleCount?: number;
  waveSpeed?: number;
  color?: 'amber' | 'gold' | 'white';
  reversed?: boolean;
}

const WavesDivider: React.FC<WavesDividerProps> = ({
  height = 80,
  waveCount = 4,
  particleCount = 25,
  waveSpeed = 12,
  color = 'amber',
  reversed = false
}) => {
  // Enhanced color schemes
  const colorSchemes = {
    amber: {
      light: 'from-amber-50',
      dark: 'to-amber-100',
      wave: 'rgba(245, 158, 11,',
      particle: 'bg-amber-400',
      shimmer: 'rgba(255, 215, 0, 0.2)'
    },
    gold: {
      light: 'from-amber-100',
      dark: 'to-yellow-200',
      wave: 'rgba(252, 211, 77,',
      particle: 'bg-yellow-300',
      shimmer: 'rgba(255, 215, 0, 0.3)'
    },
    white: {
      light: 'from-gray-50',
      dark: 'to-white',
      wave: 'rgba(245, 158, 11,',
      particle: 'bg-amber-300',
      shimmer: 'rgba(255, 255, 255, 0.4)'
    }
  };

  const selectedColor = colorSchemes[color];

  return (
    <div 
      className={`relative overflow-hidden bg-gradient-to-b ${selectedColor.light} ${selectedColor.dark}`}
      style={{ height: `${height}px` }}
    >
      {/* Enhanced wave layers with more dynamic movement */}
      {[...Array(waveCount)].map((_, layer) => (
        <motion.div
          key={layer}
          className="absolute inset-0"
          style={{
            opacity: 0.4 - layer * (0.1 + Math.random() * 0.05),
            filter: 'blur(1px)',
          }}
          animate={{
            x: reversed ? ['100%', '0%', '100%'] : ['-100%', '0%', '-100%'],
            rotate: [0, Math.random() * 8 - 4, 0],
            scaleY: [1, 1.3 + layer * 0.15, 1],
          }}
          transition={{
            duration: waveSpeed + layer * 2,
            ease: "easeInOut",
            repeat: Infinity,
            delay: layer * 0.5,
          }}
        >
          <svg
            className="absolute w-[200%] h-full"
            viewBox="0 0 1200 200"
            preserveAspectRatio="none"
          >
            <motion.path
              d={`
                M0 ${reversed ? 0 : 100}
                C ${50 + Math.random() * 350} ${reversed ? -50 : 250 + layer * 50}, 
                ${350 + Math.random() * 500} ${reversed ? 250 : -50 - layer * 50}, 
                1200 ${reversed ? 0 : 100}
                V ${reversed ? 200 : 200} H 0 V ${reversed ? 0 : 100} Z
              `}
              fill={`${selectedColor.wave}${0.8 - layer * 0.2})`}
              animate={{
                d: [
                  `
                    M0 ${reversed ? 0 : 100}
                    C ${50 + Math.random() * 350} ${reversed ? -50 : 250 + layer * 50}, 
                    ${350 + Math.random() * 500} ${reversed ? 250 : -50 - layer * 50}, 
                    1200 ${reversed ? 0 : 100}
                    V ${reversed ? 200 : 200} H 0 V ${reversed ? 0 : 100} Z
                  `,
                  `
                    M0 ${reversed ? 0 : 100}
                    C ${150 + Math.random() * 250} ${reversed ? -100 : 300 + layer * 50}, 
                    ${450 + Math.random() * 400} ${reversed ? 300 : -100 - layer * 50}, 
                    1200 ${reversed ? 0 : 100}
                    V ${reversed ? 200 : 200} H 0 V ${reversed ? 0 : 100} Z
                  `,
                  `
                    M0 ${reversed ? 0 : 100}
                    C ${50 + Math.random() * 350} ${reversed ? -50 : 250 + layer * 50}, 
                    ${350 + Math.random() * 500} ${reversed ? 250 : -50 - layer * 50}, 
                    1200 ${reversed ? 0 : 100}
                    V ${reversed ? 200 : 200} H 0 V ${reversed ? 0 : 100} Z
                  `
                ]
              }}
              transition={{
                duration: waveSpeed + layer * 3,
                ease: "easeInOut",
                repeat: Infinity,
                delay: layer * 0.5,
              }}
            />
          </svg>
        </motion.div>
      ))}

      {/* Enhanced floating particles with glow effect */}
      <div className="absolute inset-0">
        {[...Array(particleCount)].map((_, i) => {
          const size = 2 + Math.random() * 4;
          return (
            <motion.div
              key={i}
              className={`absolute rounded-full ${selectedColor.particle}`}
              style={{
                width: `${size}px`,
                height: `${size}px`,
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`,
                boxShadow: `0 0 ${size * 2}px ${size / 2}px ${selectedColor.wave}0.6)`,
                filter: 'blur(1px)'
              }}
              animate={{
                y: [-20, 20],
                x: [0, Math.random() * 30 - 15],
                opacity: [0, 0.8, 0],
                scale: [0.5, 1.2, 0.5],
                boxShadow: [
                  `0 0 ${size}px ${size / 4}px ${selectedColor.wave}0.4)`,
                  `0 0 ${size * 3}px ${size}px ${selectedColor.wave}0.7)`,
                  `0 0 ${size}px ${size / 4}px ${selectedColor.wave}0.4)`
                ]
              }}
              transition={{
                duration: 3 + Math.random() * 3,
                repeat: Infinity,
                delay: Math.random() * 2,
                ease: "easeInOut"
              }}
            />
          );
        })}
      </div>

      {/* Enhanced shimmer effect with multiple layers */}
      <motion.div
        className="absolute inset-0"
        style={{
          background: `linear-gradient(90deg, transparent, ${selectedColor.shimmer}, transparent)`,
          backgroundSize: '200% 100%',
        }}
        animate={{
          backgroundPosition: ['-200% 0', '200% 0'],
        }}
        transition={{
          duration: 3,
          repeat: Infinity,
          ease: 'linear',
        }}
      />
      
      {/* Second shimmer layer for enhanced effect */}
      <motion.div
        className="absolute inset-0"
        style={{
          background: `linear-gradient(45deg, transparent, ${selectedColor.shimmer}, transparent)`,
          backgroundSize: '200% 200%',
          opacity: 0.7
        }}
        animate={{
          backgroundPosition: ['0% 0%', '100% 100%'],
        }}
        transition={{
          duration: 5,
          repeat: Infinity,
          ease: 'easeInOut',
          delay: 1
        }}
      />
    </div>
  );
};

export default WavesDivider;
