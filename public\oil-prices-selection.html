<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Oil Prices - Jayita Edible Oil</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #fffbeb; /* amber-50 */
            background-image: url('data:image/svg+xml,%3Csvg width="100" height="100" viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg"%3E%3Cpath d="M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z" fill="%23d97706" fill-opacity="0.05" fill-rule="evenodd"/%3E%3C/svg%3E');
        }
        .card {
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            border: 1px solid rgba(217, 119, 6, 0.1);
        }
        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 25px -5px rgba(217, 119, 6, 0.1), 0 10px 10px -5px rgba(217, 119, 6, 0.2);
            border-color: rgba(217, 119, 6, 0.3);
        }
        .card-icon {
            font-size: 3rem;
            color: #d97706;
            margin-bottom: 1rem;
        }
        .btn-primary {
            background-color: #d97706;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        .btn-primary:hover {
            background-color: #b45309;
            transform: translateY(-2px);
        }
        .btn-primary::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 5px;
            height: 5px;
            background: rgba(255, 255, 255, 0.5);
            opacity: 0;
            border-radius: 100%;
            transform: scale(1, 1) translate(-50%);
            transform-origin: 50% 50%;
        }
        .btn-primary:hover::after {
            animation: ripple 1s ease-out;
        }
        @keyframes ripple {
            0% {
                transform: scale(0, 0);
                opacity: 0.5;
            }
            100% {
                transform: scale(20, 20);
                opacity: 0;
            }
        }
        .header-gradient {
            background: linear-gradient(90deg, #d97706 0%, #b45309 100%);
        }
        .page-title {
            position: relative;
            display: inline-block;
        }
        .page-title::after {
            content: '';
            position: absolute;
            width: 60%;
            height: 4px;
            background-color: #d97706;
            bottom: -10px;
            left: 20%;
            border-radius: 2px;
        }
        .price-badge {
            position: absolute;
            top: -15px;
            right: -15px;
            background-color: #b45309;
            color: white;
            border-radius: 50%;
            width: 60px;
            height: 60px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            transform: rotate(15deg);
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            z-index: 10;
        }
        .last-updated {
            font-style: italic;
            color: #78350f;
            font-size: 0.9rem;
            text-align: center;
            margin-top: -0.5rem;
            margin-bottom: 2rem;
        }
    </style>
</head>
<body>
    <header class="header-gradient text-white shadow-lg">
        <div class="container mx-auto px-4 py-6">
            <div class="flex justify-between items-center">
                <a href="/" class="text-3xl font-bold flex items-center">
                    <i class="fas fa-oil-can mr-3"></i>
                    Jayita Edible Oil
                </a>
                <nav>
                    <a href="/" class="text-white hover:text-amber-200 transition-colors duration-200 flex items-center">
                        <i class="fas fa-home mr-2"></i>
                        Back to Home
                    </a>
                </nav>
            </div>
        </div>
    </header>

    <main class="container mx-auto px-4 py-16">
        <h1 class="text-5xl font-bold text-center text-amber-800 mb-4 page-title">Oil Prices Information</h1>
        <p class="last-updated">Last updated: <span id="update-date">June 15, 2024</span></p>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-12 max-w-5xl mx-auto mt-16">
            <!-- West Bengal Prices Card -->
            <div class="card bg-white rounded-xl shadow-xl overflow-hidden relative p-8">
                <div class="price-badge">WB</div>
                <div class="text-center mb-6">
                    <i class="fas fa-map-marker-alt card-icon"></i>
                    <h2 class="text-3xl font-bold text-amber-800 mb-2">West Bengal</h2>
                    <div class="h-1 w-20 bg-amber-500 mx-auto rounded-full mb-4"></div>
                </div>
                <div class="p-4">
                    <p class="text-gray-700 mb-8 text-lg">View detailed oil prices for West Bengal with unified pricing across all regions, including wholesale and retail rates for various oil types.</p>
                    <ul class="mb-8 text-gray-700">
                        <li class="flex items-center mb-2">
                            <i class="fas fa-check-circle text-amber-600 mr-2"></i>
                            Unified pricing for all West Bengal regions
                        </li>
                        <li class="flex items-center mb-2">
                            <i class="fas fa-check-circle text-amber-600 mr-2"></i>
                            Retail, wholesale, and bulk pricing
                        </li>
                        <li class="flex items-center mb-2">
                            <i class="fas fa-check-circle text-amber-600 mr-2"></i>
                            All oil types with current rates
                        </li>
                    </ul>
                    <div class="text-center">
                        <a href="./west-bengal-prices.html" class="btn-primary inline-flex items-center px-8 py-4 text-white font-medium rounded-lg text-lg shadow-md">
                            <span>View Prices</span>
                            <i class="fas fa-arrow-right ml-2"></i>
                        </a>
                    </div>
                </div>
            </div>

            <!-- Other States Prices Card -->
            <div class="card bg-white rounded-xl shadow-xl overflow-hidden relative p-8">
                <div class="price-badge">IN</div>
                <div class="text-center mb-6">
                    <i class="fas fa-globe-asia card-icon"></i>
                    <h2 class="text-3xl font-bold text-amber-800 mb-2">Other States</h2>
                    <div class="h-1 w-20 bg-amber-500 mx-auto rounded-full mb-4"></div>
                </div>
                <div class="p-4">
                    <p class="text-gray-700 mb-8 text-lg">View our unified oil prices applicable across all Indian states and territories (excluding West Bengal). We offer consistent pricing nationwide for your convenience.</p>
                    <ul class="mb-8 text-gray-700">
                        <li class="flex items-center mb-2">
                            <i class="fas fa-check-circle text-amber-600 mr-2"></i>
                            Unified nationwide pricing
                        </li>
                        <li class="flex items-center mb-2">
                            <i class="fas fa-check-circle text-amber-600 mr-2"></i>
                            All oil types and packaging options
                        </li>
                        <li class="flex items-center mb-2">
                            <i class="fas fa-check-circle text-amber-600 mr-2"></i>
                            Wholesale and retail rates
                        </li>
                    </ul>
                    <div class="text-center">
                        <a href="./other-states-prices.html" class="btn-primary inline-flex items-center px-8 py-4 text-white font-medium rounded-lg text-lg shadow-md">
                            <span>View Prices</span>
                            <i class="fas fa-arrow-right ml-2"></i>
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Additional Information Section -->
        <div class="mt-20 max-w-4xl mx-auto bg-white rounded-xl shadow-lg p-8">
            <h2 class="text-2xl font-bold text-amber-800 mb-6 text-center">About Our Oil Prices</h2>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div class="text-center p-4">
                    <div class="rounded-full bg-amber-100 w-16 h-16 flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-sync-alt text-amber-600 text-xl"></i>
                    </div>
                    <h3 class="font-bold text-gray-800 mb-2">Daily Updates</h3>
                    <p class="text-gray-600">Our prices are updated daily to reflect the latest market trends and fluctuations.</p>
                </div>
                <div class="text-center p-4">
                    <div class="rounded-full bg-amber-100 w-16 h-16 flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-chart-line text-amber-600 text-xl"></i>
                    </div>
                    <h3 class="font-bold text-gray-800 mb-2">Market Analysis</h3>
                    <p class="text-gray-600">We provide detailed market analysis to help you understand price trends.</p>
                </div>
                <div class="text-center p-4">
                    <div class="rounded-full bg-amber-100 w-16 h-16 flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-truck text-amber-600 text-xl"></i>
                    </div>
                    <h3 class="font-bold text-gray-800 mb-2">Bulk Orders</h3>
                    <p class="text-gray-600">Special pricing available for bulk orders. Contact our sales team for details.</p>
                </div>
            </div>
        </div>
    </main>

    <footer class="bg-amber-800 text-white py-12 mt-20">
        <div class="container mx-auto px-4">
            <div class="text-center">
                <a href="/" class="inline-block mb-6">
                    <i class="fas fa-oil-can text-3xl"></i>
                </a>
                <p class="text-xl font-semibold mb-4">Jayita Edible Oil</p>
                <p>&copy; 2024 Jayita Edible Oil. All rights reserved.</p>
                <p class="mt-2">Prices are updated regularly and subject to change without notice.</p>
                <div class="mt-6 flex justify-center space-x-4">
                    <a href="#" class="text-white hover:text-amber-200 transition-colors duration-200">
                        <i class="fab fa-facebook-f"></i>
                    </a>
                    <a href="#" class="text-white hover:text-amber-200 transition-colors duration-200">
                        <i class="fab fa-twitter"></i>
                    </a>
                    <a href="#" class="text-white hover:text-amber-200 transition-colors duration-200">
                        <i class="fab fa-instagram"></i>
                    </a>
                </div>
            </div>
        </div>
    </footer>

    <script>
        // Update the date to current date
        document.addEventListener('DOMContentLoaded', function() {
            const options = { year: 'numeric', month: 'long', day: 'numeric' };
            const today = new Date().toLocaleDateString('en-US', options);
            document.getElementById('update-date').textContent = today;
        });
    </script>
</body>
</html>