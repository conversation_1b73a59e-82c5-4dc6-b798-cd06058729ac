import React from 'react';
import { motion } from 'framer-motion';

const News = () => {
  const updates = [
    {
      id: 1,
      date: 'June 15, 2023',
      title: 'New Organic Certification',
      content: 'We are proud to announce our new organic certification for all mustard oil products.'
    },
    {
      id: 2,
      date: 'May 28, 2023',
      title: 'Expanded Distribution',
      content: 'Our products are now available in 50+ new stores across the region.'
    },
    {
      id: 3,
      date: 'April 10, 2023',
      title: 'Sustainable Packaging',
      content: 'We have transitioned to 100% recyclable packaging materials.'
    }
  ];

  return (
    <section className="py-20 bg-gray-50" id="news">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div 
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5 }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl font-bold text-gray-900 mb-4">News & Updates</h2>
          <p className="text-xl text-gray-600">Stay updated with our latest developments</p>
        </motion.div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {updates.map((update) => (
            <motion.div
              key={update.id}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: 0.1 * update.id }}
              className="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow"
            >
              <div className="p-6">
                <div className="text-sm text-yellow-600 mb-2">{update.date}</div>
                <h3 className="text-xl font-semibold mb-3">{update.title}</h3>
                <p className="text-gray-600">{update.content}</p>
              </div>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default News;
