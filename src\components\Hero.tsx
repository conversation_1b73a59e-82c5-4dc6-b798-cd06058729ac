import React, { useState, useEffect } from 'react';
import './Hero.css';
import { motion, AnimatePresence } from 'framer-motion';
import { fadeIn3D, staggerContainer, hoverScale } from '../utils/motion';

const HeroUpdated = () => {
    // Array of background images for the slideshow
    const backgroundImages = [
        "/images/background-image.jpg",
        "/images/background-image2.jpg",
        "/images/background-image3.jpg",
        "/images/background-image4.jpg",

        // Add more images as needed
    ];

    // State to track the current image index
    const [currentImageIndex, setCurrentImageIndex] = useState(0);
    // State to track if slideshow is paused
    const [isPaused, setIsPaused] = useState(false);

    // Function to go to the next image
    const goToNextImage = () => {
        setCurrentImageIndex((prevIndex) => 
            prevIndex === backgroundImages.length - 1 ? 0 : prevIndex + 1
        );
    };

    // Function to go to the previous image
    const goToPrevImage = () => {
        setCurrentImageIndex((prevIndex) => 
            prevIndex === 0 ? backgroundImages.length - 1 : prevIndex - 1
        );
    };

    // Function to go to a specific image
    const goToImage = (index: number) => {
        setCurrentImageIndex(index);
    };

    // Effect to change the image every 5 seconds
    useEffect(() => {
        if (isPaused) return;
        
        const interval = setInterval(() => {
            goToNextImage();
        }, 5000); // Change image every 5 seconds

        // Clean up interval on component unmount
        return () => clearInterval(interval);
    }, [isPaused]);

    const handleSlide = (e: React.MouseEvent<HTMLButtonElement>) => {
        const button = e.currentTarget as HTMLButtonElement;
        const buttonText = button.querySelector('span') as HTMLElement;
        const container = button.closest('.relative') as HTMLElement;
        const buttonRect = button.getBoundingClientRect();
        const containerRect = container.getBoundingClientRect();
        const maxSlide = containerRect.width - buttonRect.width;
        
        // Full slide animation with exact positioning
        button.style.transition = 'transform 0.5s ease-out';
        button.style.transform = `translateX(${maxSlide}px)`;
        buttonText.style.opacity = '0';
    
        // Navigate to products after animation completes
        setTimeout(() => {
          const productsSection = document.getElementById('products');
          if (productsSection) {
            productsSection.scrollIntoView({ behavior: 'smooth' });
          }
          
          // Reset button after navigation
          setTimeout(() => {
            button.style.transition = 'transform 0.3s ease-out';
            button.style.transform = 'translateX(0)';
            buttonText.style.opacity = '1';
          }, 1000);
        }, 500);
    };

    return (
        <motion.section
            variants={staggerContainer()}
            initial="hidden"
            whileInView="show"
            viewport={{ once: true }}
            className="relative h-screen w-full overflow-hidden"
        >
            {/* Enhanced background with layered effects */}
            <motion.div className="absolute inset-0 overflow-hidden">
                {/* Image slideshow with improved transitions */}
                <AnimatePresence mode="wait">
                    <motion.div
                        key={currentImageIndex}
                        className="absolute inset-0"
                        initial={{ opacity: 0, scale: 1.05, filter: 'blur(5px)' }}
                        animate={{ opacity: 1, scale: 1, filter: 'blur(0px)' }}
                        exit={{ opacity: 0, scale: 1.02 }}
                        transition={{ 
                            duration: 1.2,
                            ease: [0.6, 0.01, 0.05, 0.95]
                        }}
                    >
                        <img
                            src={backgroundImages[currentImageIndex]}
                            alt={`Premium Oil Background ${currentImageIndex + 1}`}
                            className="w-full h-full object-cover"
                            loading="eager"
                            fetchPriority="high"
                        />
                    </motion.div>
                </AnimatePresence>

                {/* Enhanced gradient overlay */}
                <motion.div 
                    className="absolute inset-0"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ duration: 1.5 }}
                    style={{
                        background: 'radial-gradient(circle at 50% 50%, rgba(0,0,0,0.4) 0%, rgba(0,0,0,0.6) 70%, rgba(0,0,0,0.8) 100%)'
                    }}
                />

                {/* Enhanced glowing particles */}
                {[...Array(15)].map((_, i) => (
                    <motion.div
                        key={i}
                        className="absolute rounded-full bg-amber-300/40 blur-sm"
                        style={{
                            width: `${8 + Math.random() * 20}px`,
                            height: `${8 + Math.random() * 20}px`,
                            left: `${Math.random() * 100}%`,
                            top: `${Math.random() * 100}%`,
                            boxShadow: '0 0 10px 2px rgba(245, 158, 11, 0.3)'
                        }}
                        animate={{
                            y: [0, 60, 0],
                            x: [0, Math.random() * 30 - 15, 0],
                            opacity: [0.3, 0.8, 0.3],
                            scale: [1, 1.8, 1]
                        }}
                        transition={{
                            duration: 6 + Math.random() * 8,
                            repeat: Infinity,
                            ease: "easeInOut",
                            delay: Math.random() * 3
                        }}
                    />
                ))}
                
                {/* Enhanced floating oil droplets */}
                {[...Array(6)].map((_, i) => (
                    <motion.div
                        key={i}
                        className="absolute rounded-full"
                        style={{
                            width: `${15 + Math.random() * 30}px`,
                            height: `${15 + Math.random() * 30}px`,
                            left: `${Math.random() * 100}%`,
                            top: `${Math.random() * 100}%`,
                            background: `radial-gradient(circle, rgba(245, 158, 11, 0.4) 0%, rgba(245, 158, 11, 0.1) 70%)`,
                            filter: 'blur(3px)'
                        }}
                        animate={{
                            y: [0, 30, 0],
                            x: [0, Math.random() * 20 - 10, 0],
                            opacity: [0.3, 0.7, 0.3],
                            scale: [1, 1.2, 1]
                        }}
                        transition={{
                            duration: 8 + Math.random() * 10,
                            repeat: Infinity,
                            ease: "easeInOut",
                            delay: Math.random() * 5
                        }}
                    />
                ))}
            </motion.div>

            {/* Enhanced Slideshow Controls */}
            <motion.div 
                className="absolute bottom-10 left-1/2 transform -translate-x-1/2 z-20 flex space-x-3"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 1, duration: 0.8 }}
            >
                {backgroundImages.map((_, index) => (
                    <motion.button
                        key={index}
                        className={`w-3 h-3 rounded-full ${index === currentImageIndex ? 'bg-amber-400' : 'bg-white/50'}`}
                        onClick={() => goToImage(index)}
                        whileHover={{ scale: 1.3 }}
                        whileTap={{ scale: 0.9 }}
                    >
                        {index === currentImageIndex && (
                            <motion.div
                                className="absolute inset-0 rounded-full"
                                initial={{ opacity: 0 }}
                                exit={{ opacity: 0 }}
                                animate={{
                                    opacity: [0.7, 1, 0.7],
                                    boxShadow: [
                                        '0 0 5px 2px rgba(245, 158, 11, 0.5)',
                                        '0 0 10px 4px rgba(245, 158, 11, 0.7)',
                                        '0 0 5px 2px rgba(245, 158, 11, 0.5)'
                                    ]
                                }}
                                transition={{
                                    duration: 2,
                                    repeat: Infinity,
                                    ease: "easeInOut"
                                }}
                            />
                        )}
                    </motion.button>
                ))}
            </motion.div>

            {/* Enhanced Navigation Arrows */}
            <motion.button 
                className="absolute left-5 top-1/2 transform -translate-y-1/2 z-20 bg-black/30 hover:bg-amber-500/70 text-white p-3 rounded-full backdrop-blur-sm"
                onClick={goToPrevImage}
                whileHover={{ 
                    scale: 1.1,
                    boxShadow: '0 0 15px 5px rgba(245, 158, 11, 0.3)'
                }}
                whileTap={{ scale: 0.9 }}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.5, duration: 0.5 }}
            >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                </svg>
            </motion.button>
            <motion.button 
                className="absolute right-5 top-1/2 transform -translate-y-1/2 z-20 bg-black/30 hover:bg-amber-500/70 text-white p-3 rounded-full backdrop-blur-sm"
                onClick={goToNextImage}
                whileHover={{ 
                    scale: 1.1,
                    boxShadow: '0 0 15px 5px rgba(245, 158, 11, 0.3)'
                }}
                whileTap={{ scale: 0.9 }}
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.5, duration: 0.5 }}
            >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                </svg>
            </motion.button>

            {/* Enhanced Pause/Play Button */}
            <motion.button 
                className="absolute top-5 right-5 z-20 bg-black/30 hover:bg-amber-500/70 text-white p-3 rounded-full backdrop-blur-sm"
                onClick={() => setIsPaused(!isPaused)}
                whileHover={{ 
                    scale: 1.1,
                    boxShadow: '0 0 15px 5px rgba(245, 158, 11, 0.3)'
                }}
                whileTap={{ scale: 0.9 }}
                initial={{ opacity: 0, y: -20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.5, duration: 0.5 }}
            >
                {isPaused ? (
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z" />
                    </svg>
                ) : (
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 9v6m4-6v6m7-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                )}
            </motion.button>

            <div className="container mx-auto h-full flex items-center relative z-10 px-6">
                <motion.div
                    variants={fadeIn3D('right', 0.2)}
                    className="max-w-2xl text-white"
                >
                    <div className="relative overflow-hidden">
                        <motion.h1 
                            className="text-5xl md:text-6xl font-bold mb-6 leading-tight"
                            initial={{ opacity: 0, y: 50, filter: 'blur(10px)' }}
                            animate={{ opacity: 1, y: 0, filter: 'blur(0px)' }}
                            transition={{ 
                                duration: 1,
                                ease: [0.16, 1, 0.3, 1],
                                delay: 0.2
                            }}
                        >
                            Premium{' '}
                            <motion.span 
                                className="text-amber-400 relative inline-block"
                                initial={{ opacity: 0, scale: 0.8 }}
                                animate={{ opacity: 1, scale: 1 }}
                                transition={{ 
                                    type: 'spring',
                                    stiffness: 300,
                                    damping: 10,
                                    delay: 0.4
                                }}
                            >
                                <motion.span 
                                    className="absolute inset-0 bg-amber-400/20 blur-xl"
                                    animate={{
                                        opacity: [0.5, 0.8, 0.5],
                                        scale: [1, 1.1, 1]
                                    }}
                                    transition={{
                                        duration: 3,
                                        repeat: Infinity,
                                        ease: "easeInOut"
                                    }}
                                />
                                Edible Oils
                            </motion.span>{' '}
                            for Discerning Tastes
                        </motion.h1>
                        <motion.div 
                            className="absolute bottom-0 left-0 h-1 bg-gradient-to-r from-amber-300 via-amber-500 to-amber-300"
                            initial={{ scaleX: 0, originX: 0 }}
                            animate={{ scaleX: 1 }}
                            transition={{ duration: 1.2, delay: 0.6, ease: "circOut" }}
                        />
                    </div>
                    <motion.div className="overflow-hidden">
                        <motion.p 
                            className="text-xl mb-8 max-w-lg"
                            initial={{ opacity: 0, y: 20 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.8, delay: 0.8 }}
                        >
                            <motion.span
                                className="block"
                                initial={{ opacity: 0 }}
                                animate={{ opacity: 1 }}
                                transition={{ duration: 0.6, delay: 1 }}
                            >
                                Experience the purity and quality of
                            </motion.span>
                            <motion.span
                                className="block font-medium text-amber-300"
                                initial={{ opacity: 0 }}
                                animate={{ opacity: 1 }}
                                transition={{ duration: 0.6, delay: 1.2 }}
                            >
                                Jayita's premium oil selections
                            </motion.span>
                        </motion.p>
                    </motion.div>
                    <div className="relative w-full" style={{ padding: '0 1.5rem', overflow: 'visible' }}>
                        <motion.button
                            variants={hoverScale}
                            initial={{ scale: 0.95 }}
                            animate={{ 
                                scale: 1,
                                boxShadow: "0 4px 15px -5px rgba(245, 158, 11, 0.5)",
                                background: "linear-gradient(135deg, rgba(245,158,11,0.9) 0%, rgba(234,88,12,0.9) 100%)"
                            }}
                            whileHover={{
                                scale: 1.05,
                                boxShadow: "0 8px 25px -5px rgba(245, 158, 11, 0.7)",
                                background: "linear-gradient(135deg, rgba(245,158,11,0.9) 0%, rgba(234,88,12,0.9) 100%)",
                                transition: { 
                                    type: 'spring',
                                    stiffness: 400,
                                    damping: 10
                                }
                            }}
                            whileTap={{
                                scale: 0.95,
                                boxShadow: "0 2px 10px -5px rgba(245, 158, 11, 0.3)"
                            }}
                            className="px-8 py-3 text-white rounded-full font-medium text-lg relative overflow-hidden group transition-transform duration-500 ease-out backdrop-blur-sm"
                            style={{ transform: 'translateX(0)' }}
                            onClick={handleSlide}
                        >
                            <motion.span
                                className="relative z-10"
                                animate={{
                                    scale: [1, 1.05, 1],
                                    opacity: [0.9, 1, 0.9]
                                }}
                                transition={{
                                    duration: 2,
                                    repeat: Infinity,
                                    ease: "easeInOut"
                                }}
                            >
                                Explore Our Range
                            </motion.span>
                            
                            {/* Liquid effect inside button */}
                            <motion.div
                                className="absolute inset-0 rounded-full overflow-hidden"
                                initial={{ opacity: 0.2 }}
                                whileHover={{ opacity: 0.4 }}
                            >
                                <motion.div
                                    className="absolute inset-0 bg-gradient-to-r from-amber-300/40 to-transparent"
                                    animate={{
                                        x: ['-100%', '100%'],
                                    }}
                                    transition={{
                                        duration: 1.5,
                                        repeat: Infinity,
                                        ease: "easeInOut"
                                    }}
                                />
                            </motion.div>
                        </motion.button>
                        
                        {/* Enhanced glow effect around button */}
                        <motion.div
                            className="absolute rounded-full pointer-events-none"
                            style={{
                                width: '265px',
                                height: '52px',
                                top: '0',
                                left: '0',
                                border: '2px solid rgba(245, 158, 11, 0.6)',
                            }}
                            initial={{ 
                                scale: 0.98, 
                                opacity: 0.6
                            }}
                            animate={{ 
                                scale: [0.98, 1.02, 0.98],
                                opacity: [0.6, 0.9, 0.6],
                                boxShadow: [
                                    '0 0 10px 2px rgba(245, 158, 11, 0.4)',
                                    '0 0 20px 5px rgba(245, 158, 11, 0.7)',
                                    '0 0 10px 2px rgba(245, 158, 11, 0.4)'
                                ]
                            }}
                            transition={{
                                duration: 3,
                                repeat: Infinity,
                                ease: 'easeInOut'
                            }}
                        />
                    </div>
                </motion.div>
            </div>
        </motion.section>
    );
};

export default HeroUpdated;
