<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Oil Prices Widget</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #fffbeb;
            margin: 0;
            padding: 0;
        }
    </style>
</head>
<body>
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-8">
            <h2 class="text-3xl font-bold text-amber-800">Today's West Bengal Oil Prices</h2>
            <div class="flex items-center justify-center gap-2 mt-2">
                <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-clock text-amber-600">
                    <circle cx="12" cy="12" r="10"></circle>
                    <polyline points="12 6 12 12 16 14"></polyline>
                </svg>
                <p class="text-amber-600 font-medium" id="current-time">Loading time...</p>
            </div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-4">
            <div class="bg-white rounded-lg shadow-md p-6 text-center">
                <h3 class="text-xl font-bold text-gray-800 mb-2">Sunflower Oil</h3>
                <p class="text-2xl font-bold text-amber-600">₹143.3/L</p>
            </div>
            <div class="bg-white rounded-lg shadow-md p-6 text-center">
                <h3 class="text-xl font-bold text-gray-800 mb-2">Mustard Oil</h3>
                <p class="text-2xl font-bold text-amber-600">₹146.8/L</p>
            </div>
            <div class="bg-white rounded-lg shadow-md p-6 text-center">
                <h3 class="text-xl font-bold text-gray-800 mb-2">Kachi Ghani Mustard Oil</h3>
                <p class="text-2xl font-bold text-amber-600">₹149.8/L</p>
            </div>
            <div class="bg-white rounded-lg shadow-md p-6 text-center">
                <h3 class="text-xl font-bold text-gray-800 mb-2">Soybean Oil</h3>
                <p class="text-2xl font-bold text-amber-600">₹128.8/L</p>
            </div>
            <div class="bg-white rounded-lg shadow-md p-6 text-center">
                <h3 class="text-xl font-bold text-gray-800 mb-2">Palm Oil</h3>
                <p class="text-2xl font-bold text-amber-600">₹126.8/L</p>
            </div>
            <div class="bg-white rounded-lg shadow-md p-6 text-center">
                <h3 class="text-xl font-bold text-gray-800 mb-2">Cooking Oil</h3>
                <p class="text-2xl font-bold text-amber-600">₹137.8/L</p>
            </div>
        </div>

        <div class="mt-10 text-center">
            <a href="./oil-prices-selection.html" class="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md shadow-sm text-white bg-amber-600 hover:bg-amber-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-amber-500 transition-colors duration-200">
                View All Oil Prices
                <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-external-link ml-2">
                    <path d="M15 3h6v6"></path>
                    <path d="M10 14 21 3"></path>
                    <path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6"></path>
                </svg>
            </a>
        </div>
    </div>

    <script>
        // Update the time every second
        function updateTime() {
            const options = {
                timeZone: 'Asia/Kolkata',
                hour12: true,
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit',
                day: '2-digit',
                month: 'short',
                year: 'numeric'
            };

            const currentTime = new Date().toLocaleString('en-IN', options) + ' (IST)';
            document.getElementById('current-time').textContent = currentTime;
        }

        // Update time immediately and then every second
        updateTime();
        setInterval(updateTime, 1000);
    </script>
    <script src="./js/shared-prices.js"></script>
    <script>
        // Update the displayed prices from shared storage
        function updateDisplayedPrices() {
            const prices = window.getSharedPrices();

            // Get all price cards
            const priceCards = document.querySelectorAll('.grid > div');

            // Map of card titles to price object keys
            const titleToKey = {
                "Sunflower Oil": "Sunflower Oil",
                "Mustard Oil": "Mustard Oil",
                "Kachi Ghani Mustard Oil": "Kachi Ghani Mustard Oil",
                "Soybean Oil": "Soybean Oil",
                "Palm Oil": "Palm Oil",
                "Cooking Oil": "Cooking Oil"
            };

            // Update each price card
            priceCards.forEach(card => {
                const titleElement = card.querySelector('h3');
                if (titleElement) {
                    const title = titleElement.textContent.trim();
                    const key = titleToKey[title];

                    if (key && prices[key]) {
                        const priceElement = card.querySelector('p');
                        if (priceElement) {
                            priceElement.textContent = `₹${prices[key]}/L`;
                        }
                    }
                }
            });
        }

        // Listen for price updates from other pages
        window.addEventListener('oilPricesUpdated', function(e) {
            updateDisplayedPrices();
        });

        // Check for updates every minute (in case the event wasn't received)
        setInterval(updateDisplayedPrices, 60000);

        // Update prices on page load
        document.addEventListener('DOMContentLoaded', updateDisplayedPrices);

        // Add a message to show when prices were last updated
        function addLastUpdatedMessage() {
            const container = document.querySelector('.max-w-7xl');
            if (container) {
                const lastUpdatedDiv = document.createElement('div');
                lastUpdatedDiv.className = 'text-center text-amber-600 text-sm mt-4';
                lastUpdatedDiv.innerHTML = 'Prices automatically updated from West Bengal price list';
                container.appendChild(lastUpdatedDiv);
            }
        }

        document.addEventListener('DOMContentLoaded', addLastUpdatedMessage);
    </script>
</body>
</html>
