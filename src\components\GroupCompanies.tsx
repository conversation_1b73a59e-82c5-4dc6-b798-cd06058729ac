import React from 'react';
import { motion } from 'framer-motion';
import { useInView } from 'react-intersection-observer';

const GroupCompanies = () => {
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });

  interface Company {
    name: string;
    description: string;
    image: string;
    detailsLink: string;
  }

  const companies: Company[] = [
    {
      name: "Jayita Edible Oil Pvt Ltd",
      description: "Producing pure and natural edible oils since 2024",
      image: "/images/mani_casadona2.jpg",
      detailsLink: "/factory-details/jayita-oil.html"
    },
    {
      name: "Jayita Chemical Industries Pvt Ltd",
      description: "Manufacturing industrial and specialty chemicals since 2025",
      image: "/images/mani_casadona1.jpg",
      detailsLink: "/factory-details/jayita-chemicals.html"
    },
    {
      name: "Jayita Foods & Beverages Pvt Ltd",
      description: "Leading manufacturer of premium quality beverages, food products and ingredients since 2025",
      image: "/images/mani_casadona3.jpg",
      detailsLink: "/factory-details/jayita-foods.html"
    },
  ];

  return (
    <section className="py-20 bg-white" id="group">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={inView ? { opacity: 1, y: 0 } : {}}
          className="text-center mb-16"
        >
          <h2 className="text-4xl font-bold text-gray-900 mb-4">Our Group Companies</h2>
          <p className="text-xl text-gray-600">Discover our diverse portfolio of businesses</p>
        </motion.div>

        <motion.div
          ref={ref}
          initial={{ opacity: 0 }}
          animate={inView ? { opacity: 1 } : {}}
          transition={{ duration: 0.5, staggerChildren: 0.2 }}
          className="grid grid-cols-1 md:grid-cols-3 gap-8"
        >
          {companies.map((company, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              animate={inView ? { opacity: 1, y: 0 } : {}}
              transition={{ delay: index * 0.2 }}
              className="bg-white rounded-lg overflow-hidden shadow-lg hover:shadow-xl transition-shadow"
            >
              <div className="relative h-48">
                <img
                  src={company.image}
                  alt={company.name}
                  className="w-full h-full object-cover"
                />
              </div>
              <div className="p-6">
                <h3 className="text-xl font-semibold mb-2">{company.name}</h3>
                <p className="text-gray-600 mb-4">{company.description}</p>
                <a 
                  href={company.detailsLink} 
                  className="inline-block px-4 py-2 bg-yellow-500 text-white rounded-md hover:bg-yellow-600 transition-colors"
                >
                  View Factory Details
                </a>
              </div>
            </motion.div>
          ))}
        </motion.div>
      </div>
    </section>
  );
};

export default GroupCompanies;