import React, { useState, useEffect } from 'react';
import { Clock } from 'lucide-react';

const OilPricesWithClock = () => {
  const prices = [
    { name: 'Sunflower Oil', price: '₹145.3/L' },
    { name: 'Mustard Oil', price: '₹153.3/L' },
    { name: 'Kachi Ghani Mustard Oil', price: '₹157.3/L' },
    { name: 'Soybean Oil', price: '₹136.8/L' },
    { name: 'Palm Oil', price: '₹131.3/L' },
    { name: 'Cooking Oil', price: '₹141.3/L' }
  ];

  const [currentTime, setCurrentTime] = useState('');

  useEffect(() => {
    const updateTime = () => {
      const now = new Date();
      const istTime = new Date(now.getTime() + (5.5 * 60 * 60 * 1000));
      const options = {
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        day: '2-digit',
        month: 'short',
        year: 'numeric',
        hour12: true
      };
      setCurrentTime(istTime.toLocaleString('en-IN', options));
    };

    updateTime();
    const timer = setInterval(updateTime, 1000);
    return () => clearInterval(timer);
  }, []);

  return (
    <section className="py-12 bg-amber-50" id="oil-prices">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-8">
          <h2 className="text-3xl font-bold text-amber-800">Today's Oil Prices</h2>
          <div className="flex items-center justify-center gap-2 mt-2">
            <Clock className="text-amber-600" size={18} />
            <p className="text-amber-600 font-medium">{currentTime} (IST)</p>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {prices.map((oil) => (
            <div key={oil.name} className="bg-white rounded-lg shadow-md p-6 text-center">
              <h3 className="text-xl font-bold text-gray-800 mb-2">{oil.name}</h3>
              <p className="text-2xl font-bold text-amber-600">{oil.price}</p>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default OilPricesWithClock;
