.sliding-animation {
  transform: translateX(calc(100vw - 100%)) !important;
  transition: transform 1s ease-out !important;
}

/* Slideshow styles */
.slideshow-container {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.slideshow-image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: opacity 1s ease-in-out;
}

.slideshow-controls {
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 10px;
  z-index: 10;
}

.slideshow-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.5);
  cursor: pointer;
  transition: all 0.3s ease;
}

.slideshow-dot.active {
  background-color: #f59e0b;
  transform: scale(1.2);
}

.slideshow-arrow {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  width: 40px;
  height: 40px;
  background-color: rgba(0, 0, 0, 0.3);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 10;
  transition: background-color 0.3s ease;
}

.slideshow-arrow:hover {
  background-color: rgba(0, 0, 0, 0.5);
}

.slideshow-arrow.left {
  left: 20px;
}

.slideshow-arrow.right {
  right: 20px;
}

.slideshow-pause {
  position: absolute;
  top: 20px;
  right: 20px;
  width: 40px;
  height: 40px;
  background-color: rgba(0, 0, 0, 0.3);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 10;
  transition: background-color 0.3s ease;
}

.slideshow-pause:hover {
  background-color: rgba(0, 0, 0, 0.5);
}
