<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>Mr. <PERSON><PERSON><PERSON> - Director & Legal Adviser - <PERSON><PERSON> Edible Oil</title>
  <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@400;500;700&family=Playfair+Display:wght@400;700&display=swap" rel="stylesheet" />
  <style>
    /* General Styles */
    body {
      font-family: 'Roboto', sans-serif;
      margin: 0;
      padding: 0;
      background: linear-gradient(to bottom, #fef3c7, #fde68a); /* Amber theme */
      color: #1f2937;
      line-height: 1.6;
    }

    header {
      background: linear-gradient(135deg, #d97706, #f59e0b); /* Amber gradient for header */
      color: #fff;
      padding: 3rem 1rem;
      text-align: center;
      box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
    }
    header h1 {
      font-size: 3rem;
      font-family: 'Playfair Display', serif;
      text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.3);
      margin-bottom: 0.5rem;
    }
    header p {
      font-size: 1.5rem;
      margin-top: 0;
      opacity: 0.9;
    }

    nav {
      margin-top: 1.5rem;
    }
    nav a {
      color: #fff;
      text-decoration: none;
      font-weight: bold;
      padding: 0.5rem 1rem;
      border: 2px solid white;
      border-radius: 8px;
      transition: all 0.3s ease;
    }
    nav a:hover {
      background-color: white;
      color: #d97706;
    }

    .container {
      max-width: 1000px;
      margin: 3rem auto;
      padding: 2rem;
      background: #ffffff;
      border-radius: 20px;
      box-shadow: 0 10px 20px rgba(0, 0, 0, 0.15);
      border: 1px solid #d1d5db;
    }

    .profile-header {
      display: flex;
      flex-direction: column;
      align-items: center;
      margin-bottom: 2rem;
      padding-bottom: 2rem;
      border-bottom: 2px solid #fde68a;
    }

    .profile-image {
      width: 250px;
      height: 250px;
      border-radius: 15px;
      border: 8px solid #d97706;
      padding: 5px;
      background-color: white;
      box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
      margin-bottom: 1.5rem;
      object-fit: cover;
      object-position: center top;
    }

    .profile-title {
      text-align: center;
    }
    .profile-title h2 {
      font-size: 2.5rem;
      font-family: 'Playfair Display', serif;
      color: #1f2937;
      margin-bottom: 0.5rem;
    }
    .profile-title p {
      font-size: 1.5rem;
      color: #d97706;
      font-weight: 500;
      margin-top: 0;
    }

    .profile-details {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 2rem;
      margin-bottom: 2rem;
    }

    .detail-card {
      background: linear-gradient(135deg, #fff7ed, #ffedd5);
      border: 1px solid #fcd34d;
      border-radius: 12px;
      padding: 1.5rem;
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }
    .detail-card h3 {
      font-size: 1.3rem;
      color: #92400e;
      margin-top: 0;
      border-bottom: 2px solid #fcd34d;
      padding-bottom: 0.5rem;
      margin-bottom: 1rem;
    }
    .detail-card p {
      margin: 0.5rem 0;
    }

    .biography {
      background: linear-gradient(135deg, #fff7ed, #ffedd5);
      border: 1px solid #fcd34d;
      border-radius: 12px;
      padding: 2rem;
      margin-bottom: 2rem;
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }
    .biography h3 {
      font-size: 1.8rem;
      color: #92400e;
      margin-top: 0;
      border-bottom: 2px solid #fcd34d;
      padding-bottom: 0.5rem;
      margin-bottom: 1rem;
    }
    .biography p {
      font-size: 1.1rem;
      line-height: 1.8;
      text-align: justify;
    }

    .achievements {
      background: linear-gradient(135deg, #fff7ed, #ffedd5);
      border: 1px solid #fcd34d;
      border-radius: 12px;
      padding: 2rem;
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }
    .achievements h3 {
      font-size: 1.8rem;
      color: #92400e;
      margin-top: 0;
      border-bottom: 2px solid #fcd34d;
      padding-bottom: 0.5rem;
      margin-bottom: 1rem;
    }
    .achievements ul {
      padding-left: 1.5rem;
    }
    .achievements li {
      margin-bottom: 0.8rem;
      font-size: 1.1rem;
    }

    .back-to-team {
      text-align: center;
      margin: 2rem 0;
    }
    .back-to-team a {
      display: inline-block;
      background-color: #d97706;
      color: white;
      padding: 1rem 2rem;
      border-radius: 8px;
      text-decoration: none;
      font-weight: bold;
      transition: all 0.3s ease;
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    }
    .back-to-team a:hover {
      background-color: #b45309;
      transform: translateY(-3px);
      box-shadow: 0 6px 12px rgba(0, 0, 0, 0.3);
    }

    footer {
      background: linear-gradient(135deg, #d97706, #f59e0b);
      color: #fff;
      padding: 2rem;
      text-align: center;
      font-size: 1rem;
      border-top: 2px solid #f59e0b;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
      .profile-header {
        flex-direction: column;
      }
      .profile-image {
        margin-right: 0;
        margin-bottom: 1.5rem;
      }
      .profile-details {
        grid-template-columns: 1fr;
      }
    }
  </style>
</head>
<body>
  <header>
    <h1>Mr. Kutubuddin Mondal</h1>
    <p>Managing Director of Jayita Chemical industries Pvt Ltd</p>
    <nav>
      <a href="index.html">Back to Home</a>
      <a href="management-details-new.html">Management Team</a>
    </nav>
  </header>

  <div class="container">
    <div class="profile-header">
      <img src="/images/management/MD.jpg" alt="Mr. Kutubuddin Mondal" class="profile-image" />
      <div class="profile-title">
        <h2>Mr. Kutubuddin Mondal</h2>
        <p>Managing Director of Jayita Chemical industries Pvt Ltd</p>
      </div>
    </div>

    <div class="profile-details">
      <div class="detail-card">
        <h3>Professional Information</h3>
        <p><strong>Position:</strong> Managing Director of Jayita Chemical industries Pvt Ltd</p>
        <p><strong>Experience:</strong> 18+ years in international trade</p>
        <p><strong>Location:</strong> Mumbai, Maharashtra</p>
      </div>

      <div class="detail-card">
        <h3>Areas of Expertise</h3>
        <p>• Legal Affairs</p>
        <p>• Financial Operations</p>
        <p>• Corporate Strategy</p>
        <p>• Mergers & Acquisitions</p>
        <p>• Social Work</p>
      </div>
    </div>

    <div class="biography">
      <h3>Biography</h3>
      <p>Mr. Kutubuddin Mondal is an eminent Lawyer and Social worker. He currently oversees Jayita Edible Oil Pvt Ltd as the head of Legal and Financial operations.</p>
      <p>With over 18 years of experience in international trade, Mr. Mondal brings valuable legal and financial expertise to the company. He is dedicated to exploring and improving strategies related to retention, acquisition, amalgamation, and mergers, making key decisions for the company while keeping Jayita's future goals in mind.</p>
      <p>His legal background and financial acumen have been instrumental in navigating complex business environments and ensuring the company's operations remain compliant with all relevant regulations while maximizing financial performance.</p>
    </div>

    <div class="achievements">
      <h3>Key Achievements</h3>
      <ul>
        <li>Established himself as a respected lawyer with expertise in corporate law</li>
        <li>Implemented effective legal and financial frameworks at Jayita Edible Oil</li>
        <li>Successfully managed international trade relationships and agreements</li>
        <li>Developed strategies for corporate growth through mergers and acquisitions</li>
        <li>Contributed to social welfare initiatives alongside corporate responsibilities</li>
      </ul>
    </div>

    <div class="back-to-team">
      <a href="management-details-new.html">Back to Management Team</a>
    </div>
  </div>

  <footer>
    <p>&copy; 2025 Jayita Edible Oil. All rights reserved.</p>
  </footer>
</body>
</html>