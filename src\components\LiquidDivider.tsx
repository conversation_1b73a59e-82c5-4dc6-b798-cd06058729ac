import React from 'react';
import { motion } from 'framer-motion';

interface LiquidDividerProps {
  position?: 'top' | 'bottom';
  color?: string;
  height?: number;
  className?: string;
}

const LiquidDivider: React.FC<LiquidDividerProps> = ({
  position = 'bottom',
  color = '#ffffff',
  height = 120,
  className = '',
}) => {
  const isTop = position === 'top';
  
  return (
    <div 
      className={`relative w-full overflow-hidden ${className}`} 
      style={{ height: `${height}px` }}
    >
      <svg
        className="absolute w-full h-full"
        preserveAspectRatio="none"
        viewBox="0 0 1440 320"
        style={{ 
          transform: isTop ? 'rotate(180deg)' : 'none',
          top: 0,
          left: 0
        }}
      >
        <motion.path
          initial={{ 
            d: `M0,${isTop ? 0 : 320} C180,${isTop ? 100 : 220} 360,${isTop ? 0 : 320} 540,${isTop ? 100 : 220} C720,${isTop ? 200 : 120} 900,${isTop ? 0 : 320} 1080,${isTop ? 100 : 220} C1260,${isTop ? 200 : 120} 1440,${isTop ? 0 : 320} 1440,${isTop ? 0 : 320} L1440,${isTop ? 320 : 0} L0,${isTop ? 320 : 0} Z`
          }}
          animate={{
            d: [
              `M0,${isTop ? 0 : 320} C180,${isTop ? 100 : 220} 360,${isTop ? 0 : 320} 540,${isTop ? 100 : 220} C720,${isTop ? 200 : 120} 900,${isTop ? 0 : 320} 1080,${isTop ? 100 : 220} C1260,${isTop ? 200 : 120} 1440,${isTop ? 0 : 320} 1440,${isTop ? 0 : 320} L1440,${isTop ? 320 : 0} L0,${isTop ? 320 : 0} Z`,
              `M0,${isTop ? 0 : 320} C180,${isTop ? 200 : 120} 360,${isTop ? 0 : 320} 540,${isTop ? 200 : 120} C720,${isTop ? 100 : 220} 900,${isTop ? 0 : 320} 1080,${isTop ? 200 : 120} C1260,${isTop ? 100 : 220} 1440,${isTop ? 0 : 320} 1440,${isTop ? 0 : 320} L1440,${isTop ? 320 : 0} L0,${isTop ? 320 : 0} Z`,
              `M0,${isTop ? 0 : 320} C180,${isTop ? 100 : 220} 360,${isTop ? 0 : 320} 540,${isTop ? 100 : 220} C720,${isTop ? 200 : 120} 900,${isTop ? 0 : 320} 1080,${isTop ? 100 : 220} C1260,${isTop ? 200 : 120} 1440,${isTop ? 0 : 320} 1440,${isTop ? 0 : 320} L1440,${isTop ? 320 : 0} L0,${isTop ? 320 : 0} Z`
            ]
          }}
          transition={{
            repeat: Infinity,
            repeatType: "reverse",
            duration: 20,
            ease: "easeInOut"
          }}
          fill={color}
        />
        
        {/* Second layer with different timing for more complex effect */}
        <motion.path
          initial={{ 
            d: `M0,${isTop ? 0 : 320} C180,${isTop ? 150 : 170} 360,${isTop ? 50 : 270} 540,${isTop ? 150 : 170} C720,${isTop ? 250 : 70} 900,${isTop ? 50 : 270} 1080,${isTop ? 150 : 170} C1260,${isTop ? 250 : 70} 1440,${isTop ? 50 : 270} 1440,${isTop ? 0 : 320} L1440,${isTop ? 320 : 0} L0,${isTop ? 320 : 0} Z`
          }}
          animate={{
            d: [
              `M0,${isTop ? 0 : 320} C180,${isTop ? 150 : 170} 360,${isTop ? 50 : 270} 540,${isTop ? 150 : 170} C720,${isTop ? 250 : 70} 900,${isTop ? 50 : 270} 1080,${isTop ? 150 : 170} C1260,${isTop ? 250 : 70} 1440,${isTop ? 50 : 270} 1440,${isTop ? 0 : 320} L1440,${isTop ? 320 : 0} L0,${isTop ? 320 : 0} Z`,
              `M0,${isTop ? 0 : 320} C180,${isTop ? 250 : 70} 360,${isTop ? 150 : 170} 540,${isTop ? 250 : 70} C720,${isTop ? 150 : 170} 900,${isTop ? 50 : 270} 1080,${isTop ? 250 : 70} C1260,${isTop ? 150 : 170} 1440,${isTop ? 50 : 270} 1440,${isTop ? 0 : 320} L1440,${isTop ? 320 : 0} L0,${isTop ? 320 : 0} Z`,
              `M0,${isTop ? 0 : 320} C180,${isTop ? 150 : 170} 360,${isTop ? 50 : 270} 540,${isTop ? 150 : 170} C720,${isTop ? 250 : 70} 900,${isTop ? 50 : 270} 1080,${isTop ? 150 : 170} C1260,${isTop ? 250 : 70} 1440,${isTop ? 50 : 270} 1440,${isTop ? 0 : 320} L1440,${isTop ? 320 : 0} L0,${isTop ? 320 : 0} Z`
            ]
          }}
          transition={{
            repeat: Infinity,
            repeatType: "reverse",
            duration: 15,
            ease: "easeInOut"
          }}
          fill={color}
          opacity={0.6}
        />
        
        {/* Highlight layer for premium effect */}
        <motion.path
          initial={{ 
            d: `M0,${isTop ? 30 : 290} C180,${isTop ? 130 : 190} 360,${isTop ? 30 : 290} 540,${isTop ? 130 : 190} C720,${isTop ? 230 : 90} 900,${isTop ? 30 : 290} 1080,${isTop ? 130 : 190} C1260,${isTop ? 230 : 90} 1440,${isTop ? 30 : 290} 1440,${isTop ? 30 : 290} L1440,${isTop ? 320 : 0} L0,${isTop ? 320 : 0} Z`
          }}
          animate={{
            d: [
              `M0,${isTop ? 30 : 290} C180,${isTop ? 130 : 190} 360,${isTop ? 30 : 290} 540,${isTop ? 130 : 190} C720,${isTop ? 230 : 90} 900,${isTop ? 30 : 290} 1080,${isTop ? 130 : 190} C1260,${isTop ? 230 : 90} 1440,${isTop ? 30 : 290} 1440,${isTop ? 30 : 290} L1440,${isTop ? 320 : 0} L0,${isTop ? 320 : 0} Z`,
              `M0,${isTop ? 30 : 290} C180,${isTop ? 230 : 90} 360,${isTop ? 30 : 290} 540,${isTop ? 230 : 90} C720,${isTop ? 130 : 190} 900,${isTop ? 30 : 290} 1080,${isTop ? 230 : 90} C1260,${isTop ? 130 : 190} 1440,${isTop ? 30 : 290} 1440,${isTop ? 30 : 290} L1440,${isTop ? 320 : 0} L0,${isTop ? 320 : 0} Z`,
              `M0,${isTop ? 30 : 290} C180,${isTop ? 130 : 190} 360,${isTop ? 30 : 290} 540,${isTop ? 130 : 190} C720,${isTop ? 230 : 90} 900,${isTop ? 30 : 290} 1080,${isTop ? 130 : 190} C1260,${isTop ? 230 : 90} 1440,${isTop ? 30 : 290} 1440,${isTop ? 30 : 290} L1440,${isTop ? 320 : 0} L0,${isTop ? 320 : 0} Z`
            ]
          }}
          transition={{
            repeat: Infinity,
            repeatType: "reverse",
            duration: 25,
            ease: "easeInOut"
          }}
          fill={color}
          opacity={0.3}
        />
      </svg>
    </div>
  );
};

export default LiquidDivider;